import Header from "@/components/Header";
import Footer from "@/components/Footer";
import Image from "next/image";
import { client } from "@/lib/sanity";
import { urlForImage } from "@/lib/sanity";
import { playersQuery, staffQuery } from "@/lib/queries";

export const metadata = {
  title: "Our Team | Northern Nepalese United FC",
  description: "Meet the players and coaching staff of Northern Nepalese United FC.",
};

// This makes the page dynamic and will revalidate every 60 seconds
export const revalidate = 60;

// Define types for player and staff
interface PlayerStats {
  appearances: number;
  goals?: number;
  assists?: number;
  cleanSheets?: number;
}

interface SanityImageReference {
  _type: string;
  asset: {
    _ref: string;
    _type: string;
  };
}

interface Player {
  _id: string;
  name: string;
  position: string;
  role: string;
  jerseyNumber: number;
  image?: SanityImageReference;
  stats: PlayerStats;
  bio?: string;
}

interface Staff {
  _id: string;
  name: string;
  role: string;
  image?: SanityImageReference;
  bio?: string;
  contactInfo?: {
    email?: string;
    phone?: string;
  };
}

// Functions to fetch data from Sanity
async function getPlayers() {
  try {
    return await client.fetch(playersQuery);
  } catch (error) {
    console.error("Failed to fetch players:", error);
    return [];
  }
}

async function getStaff() {
  try {
    return await client.fetch(staffQuery);
  } catch (error) {
    console.error("Failed to fetch staff:", error);
    return [];
  }
}

export default async function TeamPage() {
  // Fetch data from Sanity - this will work once you have data in Sanity
  // For now, we'll use placeholder data as fallback
  let players: Player[] = await getPlayers();
  let staff: Staff[] = await getStaff();

  // Use fallback data if no data is returned from Sanity
  if (!players || players.length === 0) {
    players = [
      {
        _id: "1",
        name: "Ram Gurung",
        position: "Forward",
        role: "Captain",
        jerseyNumber: 9,
        stats: {
          appearances: 15,
          goals: 8,
          assists: 3,
        },
      },
      {
        _id: "2",
        name: "Suman Tamang",
        position: "Midfielder",
        role: "Player",
        jerseyNumber: 10,
        stats: {
          appearances: 18,
          goals: 4,
          assists: 9,
        },
      },
      {
        _id: "3",
        name: "Anish Rai",
        position: "Defender",
        role: "Player",
        jerseyNumber: 4,
        stats: {
          appearances: 20,
          goals: 1,
          assists: 2,
        },
      },
      {
        _id: "4",
        name: "Bipin Thapa",
        position: "Goalkeeper",
        role: "Player",
        jerseyNumber: 1,
        stats: {
          appearances: 17,
          cleanSheets: 5,
        },
      },
      {
        _id: "5",
        name: "Nabin Shrestha",
        position: "Midfielder",
        role: "Player",
        jerseyNumber: 8,
        stats: {
          appearances: 16,
          goals: 3,
          assists: 5,
        },
      },
      {
        _id: "6",
        name: "Sanjeev Magar",
        position: "Defender",
        role: "Player",
        jerseyNumber: 5,
        stats: {
          appearances: 14,
          goals: 0,
          assists: 1,
        },
      },
    ];
  }

  if (!staff || staff.length === 0) {
    staff = [
      {
        _id: "1",
        name: "Krishna Pun",
        role: "Head Coach",
        bio: "Former professional player with 10+ years of coaching experience.",
      },
      {
        _id: "2",
        name: "Binod Khatri",
        role: "Assistant Coach",
        bio: "Specializes in tactical development and player conditioning.",
      },
      {
        _id: "3",
        name: "Laxmi Thapa",
        role: "Team Manager",
        bio: "Handles team logistics, scheduling, and administration.",
      },
    ];
  }

  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <section className="bg-primary-800 text-white py-16">
          <div className="container">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Our Team</h1>
            <p className="text-xl max-w-3xl">
              Meet the players and coaching staff who represent Northern Nepalese United FC.
            </p>
          </div>
        </section>

        {/* Players Section */}
        <section className="py-16">
          <div className="container">
            <h2 className="text-3xl font-bold mb-10 text-center">Players</h2>

            {/* Captain Section */}
            {(() => {
              const captain = players.find(player => player.role === "Captain");
              if (captain) {
                return (
                  <div className="mb-12">
                    <h3 className="text-2xl font-bold mb-6 text-center text-primary-600">Team Captain</h3>
                    <div className="max-w-md mx-auto">
                      <div className="bg-gradient-to-br from-primary-50 to-primary-100 rounded-lg shadow-lg overflow-hidden border-2 border-primary-200">
                        <div className="h-48 bg-neutral-100 relative">
                          {captain.image ? (
                            <Image
                              src={urlForImage(captain.image).url()}
                              alt={captain.name}
                              fill
                              className="object-cover"
                            />
                          ) : (
                            <Image
                              src="/placeholder-player.svg"
                              alt={captain.name}
                              fill
                              className="object-cover"
                            />
                          )}
                          <div className="absolute top-0 right-0 bg-yellow-500 text-white p-3 font-bold text-xl">
                            #{captain.jerseyNumber}
                          </div>
                          <div className="absolute top-0 left-0 bg-primary-600 text-white px-3 py-1 text-sm font-semibold">
                            CAPTAIN
                          </div>
                        </div>

                        <div className="p-6">
                          <h4 className="text-2xl font-bold mb-2 text-center">{captain.name}</h4>
                          <p className="text-primary-600 font-medium mb-4 text-center text-lg">{captain.position}</p>

                          <div className="grid grid-cols-3 gap-4 border-t border-primary-200 pt-4 text-center">
                            <div>
                              <p className="text-xs text-neutral-600 font-medium">Appearances</p>
                              <p className="font-bold text-xl text-primary-700">{captain.stats?.appearances || 0}</p>
                            </div>
                            <div>
                              <p className="text-xs text-neutral-600 font-medium">Goals</p>
                              <p className="font-bold text-xl text-primary-700">
                                {captain.position === "Goalkeeper" ? "-" : captain.stats?.goals || 0}
                              </p>
                            </div>
                            <div>
                              <p className="text-xs text-neutral-600 font-medium">
                                {captain.position === "Goalkeeper" ? "Clean Sheets" : "Assists"}
                              </p>
                              <p className="font-bold text-xl text-primary-700">
                                {captain.position === "Goalkeeper"
                                  ? captain.stats?.cleanSheets || 0
                                  : captain.stats?.assists || 0
                                }
                              </p>
                            </div>
                          </div>

                          {captain.bio && (
                            <div className="mt-4 pt-4 border-t border-primary-200">
                              <p className="text-sm text-neutral-700 text-center">{captain.bio}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                );
              }
              return null;
            })()}

            {/* Other Players Section - Enhanced Design */}
            {(() => {
              const otherPlayers = players.filter(player => player.role !== "Captain");
              if (otherPlayers.length > 0) {
                // Group players by position for better organization
                const playersByPosition = otherPlayers.reduce((acc, player) => {
                  const position = player.position;
                  if (!acc[position]) {
                    acc[position] = [];
                  }
                  acc[position].push(player);
                  return acc;
                }, {} as Record<string, Player[]>);

                const positionOrder = ["Goalkeeper", "Defender", "Midfielder", "Forward"];
                const getPositionColor = (position: string) => {
                  switch (position) {
                    case "Goalkeeper": return "bg-green-500";
                    case "Defender": return "bg-blue-500";
                    case "Midfielder": return "bg-purple-500";
                    case "Forward": return "bg-red-500";
                    default: return "bg-gray-500";
                  }
                };

                return (
                  <div>
                    <h3 className="text-3xl font-bold mb-8 text-center">Squad</h3>

                    {positionOrder.map((position) => {
                      const positionPlayers = playersByPosition[position];
                      if (!positionPlayers || positionPlayers.length === 0) return null;

                      return (
                        <div key={position} className="mb-12">
                          <div className="flex items-center justify-center mb-6">
                            <div className={`${getPositionColor(position)} w-4 h-4 rounded-full mr-3`}></div>
                            <h4 className="text-2xl font-bold text-gray-800">{position}s</h4>
                            <div className={`${getPositionColor(position)} w-4 h-4 rounded-full ml-3`}></div>
                          </div>

                          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                            {positionPlayers.map((player) => (
                              <div key={player._id} className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden border border-gray-100">
                                {/* Player Image Section */}
                                <div className="relative h-48 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden">
                                  {player.image ? (
                                    <Image
                                      src={urlForImage(player.image).url()}
                                      alt={player.name}
                                      fill
                                      className="object-cover group-hover:scale-110 transition-transform duration-300"
                                    />
                                  ) : (
                                    <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-primary-100 to-primary-200">
                                      <svg className="w-16 h-16 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                      </svg>
                                    </div>
                                  )}

                                  {/* Jersey Number Badge */}
                                  <div className={`absolute top-3 left-3 ${getPositionColor(player.position)} text-white text-sm font-bold px-3 py-1 rounded-full shadow-lg`}>
                                    #{player.jerseyNumber}
                                  </div>

                                  {/* Position Badge */}
                                  <div className="absolute top-3 right-3 bg-white/90 backdrop-blur-sm text-gray-800 text-xs font-semibold px-2 py-1 rounded-full">
                                    {player.position}
                                  </div>

                                  {/* Vice Captain Badge */}
                                  {player.role === "Vice Captain" && (
                                    <div className="absolute bottom-3 left-3 bg-orange-500 text-white px-2 py-1 text-xs font-semibold rounded-full">
                                      VICE CAPTAIN
                                    </div>
                                  )}
                                </div>

                                {/* Player Info Section */}
                                <div className="p-5">
                                  <h4 className="text-xl font-bold mb-2 text-gray-800 group-hover:text-primary-600 transition-colors">
                                    {player.name}
                                  </h4>

                                  {/* Stats Grid */}
                                  <div className="grid grid-cols-3 gap-3 mt-4">
                                    <div className="text-center bg-gray-50 rounded-lg p-3 group-hover:bg-primary-50 transition-colors">
                                      <p className="text-xs text-gray-500 font-medium mb-1">Apps</p>
                                      <p className="font-bold text-lg text-gray-800">{player.stats?.appearances || 0}</p>
                                    </div>
                                    <div className="text-center bg-gray-50 rounded-lg p-3 group-hover:bg-primary-50 transition-colors">
                                      <p className="text-xs text-gray-500 font-medium mb-1">
                                        {player.position === "Goalkeeper" ? "Saves" : "Goals"}
                                      </p>
                                      <p className="font-bold text-lg text-gray-800">
                                        {player.position === "Goalkeeper" ? "-" : player.stats?.goals || 0}
                                      </p>
                                    </div>
                                    <div className="text-center bg-gray-50 rounded-lg p-3 group-hover:bg-primary-50 transition-colors">
                                      <p className="text-xs text-gray-500 font-medium mb-1">
                                        {player.position === "Goalkeeper" ? "Clean" : "Assists"}
                                      </p>
                                      <p className="font-bold text-lg text-gray-800">
                                        {player.position === "Goalkeeper"
                                          ? player.stats?.cleanSheets || 0
                                          : player.stats?.assists || 0
                                        }
                                      </p>
                                    </div>
                                  </div>

                                  {/* Bio Preview */}
                                  {player.bio && (
                                    <p className="text-sm text-gray-600 mt-3 line-clamp-2">
                                      {player.bio}
                                    </p>
                                  )}
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                );
              }
              return null;
            })()}
          </div>
        </section>

        {/* Coaching Staff Section - Enhanced Design */}
        <section className="py-16 bg-gradient-to-br from-neutral-50 to-neutral-100">
          <div className="container">
            <div className="text-center mb-12">
              <h2 className="text-4xl font-bold mb-4 text-gray-800">Coaching Staff</h2>
              <div className="w-24 h-1 bg-primary-600 mx-auto mb-4"></div>
              <p className="text-gray-600 max-w-2xl mx-auto">
                Meet the experienced professionals who guide and develop our players both on and off the field.
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {staff.map((member) => (
                <div key={member._id} className="group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden border border-gray-100">
                  {/* Staff Image Section */}
                  <div className="relative h-56 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden">
                    {member.image ? (
                      <Image
                        src={urlForImage(member.image).url()}
                        alt={member.name}
                        fill
                        className="object-cover group-hover:scale-110 transition-transform duration-300"
                      />
                    ) : (
                      <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-primary-100 to-primary-200">
                        <svg className="w-20 h-20 text-primary-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                      </div>
                    )}

                    {/* Role Badge */}
                    <div className="absolute top-3 right-3 bg-primary-600 text-white text-xs font-semibold px-3 py-1 rounded-full shadow-lg">
                      {member.role}
                    </div>
                  </div>

                  {/* Staff Info Section */}
                  <div className="p-6">
                    <h3 className="text-2xl font-bold mb-2 text-gray-800 group-hover:text-primary-600 transition-colors">
                      {member.name}
                    </h3>

                    {member.bio && (
                      <p className="text-gray-600 mb-4 leading-relaxed">
                        {member.bio}
                      </p>
                    )}

                    {/* Contact Information */}
                    {member.contactInfo && (member.contactInfo.email || member.contactInfo.phone) && (
                      <div className="bg-gray-50 rounded-lg p-4 group-hover:bg-primary-50 transition-colors">
                        <h4 className="text-sm font-semibold text-gray-700 mb-2">Contact Information</h4>
                        {member.contactInfo.email && (
                          <div className="flex items-center mb-2">
                            <svg className="h-4 w-4 mr-3 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                            </svg>
                            <span className="text-sm text-gray-600">{member.contactInfo.email}</span>
                          </div>
                        )}
                        {member.contactInfo.phone && (
                          <div className="flex items-center">
                            <svg className="h-4 w-4 mr-3 text-primary-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                            </svg>
                            <span className="text-sm text-gray-600">{member.contactInfo.phone}</span>
                          </div>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>

        {/* Join the Team CTA */}
        <section className="py-4 bg-primary-800 text-white">
          <div className="container">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl font-bold mb-4">Join Our Team</h2>
              <p className="text-lg mb-8">
                We&apos;re always looking for new players to join our club. Whether you&apos;re experienced or just starting out, there&apos;s a place for you at Northern Nepalese United FC.
              </p>
              <a
                href="/contact"
                className="bg-white text-primary-700 hover:bg-primary-50 px-8 py-3 rounded-lg font-semibold inline-flex items-center"
              >
                Contact Us to Join
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 ml-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                </svg>
              </a>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}