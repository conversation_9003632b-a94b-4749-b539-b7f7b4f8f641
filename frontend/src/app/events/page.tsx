import Header from "@/components/Header";
import Footer from "@/components/Footer";
import Link from "next/link";
import { client } from "@/lib/sanity";
import { Event } from "@/types";

export const metadata = {
  title: "Events & Fixtures | Northern Nepalese United FC",
  description: "View upcoming matches, training sessions, and social events for Northern Nepalese United Football Club.",
};

// This makes the page dynamic and will revalidate every 60 seconds
export const revalidate = 60;

async function getEvents() {
  return await client.fetch(`
    *[_type == "event"] | order(date desc) {
      _id,
      title,
      slug,
      date,
      location,
      eventType,
      opponent,
      homeOrAway,
      result
    }
  `);
}

export default async function EventsPage() {
  const allEvents = await getEvents();
  
  // Split events into upcoming and past
  const now = new Date().toISOString();
  const upcomingEvents = allEvents.filter((event: Event) => event.date > now);
  const pastEvents = allEvents.filter((event: Event) => event.date <= now);
  
  // Sort upcoming events by date (ascending - nearest first)
  upcomingEvents.sort((a: Event, b: Event) => new Date(a.date).getTime() - new Date(b.date).getTime());
  
  // Sort past events by date (descending - most recent first)
  pastEvents.sort((a: Event, b: Event) => new Date(b.date).getTime() - new Date(a.date).getTime());
  
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <section className="bg-primary-800 text-white py-16">
          <div className="container">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">Events & Fixtures</h1>
            <p className="text-xl max-w-3xl">
              Stay updated with all upcoming matches, training sessions, and social events for Northern Nepalese United FC.
            </p>
          </div>
        </section>

        {/* Upcoming Events Section */}
        <section className="py-16">
          <div className="container">
            <h2 className="text-3xl font-bold mb-8">Upcoming Events</h2>
            
            {upcomingEvents.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {upcomingEvents.map((event: Event) => (
                  <div key={event._id} className={`
                    bg-white border border-neutral-200 rounded-lg p-8 shadow-lg
                    hover:scale-105 transform transition-all duration-300 ease-out
                    border-t-4 ${
                      event.eventType === "match" ? "border-t-primary-500" :
                      event.eventType === "training" ? "border-t-secondary-500" :
                      "border-t-neutral-500"
                    }
                  `}>
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-xl font-bold mb-2">{event.title}</h3>
                        <p className="text-neutral-600 mb-1">
                          {new Date(event.date).toLocaleDateString("en-US", {
                            weekday: "long",
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          })}
                          {" at "}
                          {new Date(event.date).toLocaleTimeString("en-US", {
                            hour: "2-digit",
                            minute: "2-digit",
                          })}
                        </p>
                        {event.location && <p className="text-neutral-600 mb-2">{event.location}</p>}
                      </div>
                      {event.eventType && (
                        <span className={`text-sm px-3 py-1.5 rounded uppercase font-bold ${
                          event.eventType === "match" 
                            ? "bg-primary-100 text-primary-800" 
                            : event.eventType === "training" 
                            ? "bg-secondary-100 text-secondary-800"
                            : "bg-neutral-100 text-neutral-800"
                        }`}>
                          {event.eventType}
                        </span>
                      )}
                    </div>

                    {event.eventType === "match" && event.opponent && (
                      <div className="mt-3">
                        <p className="text-neutral-800">
                          <span className="font-medium">
                            {event.homeOrAway === "Home" ? "Home game vs " : "Away game @ "}
                            {event.opponent}
                          </span>
                        </p>
                      </div>
                    )}

                    {event.slug && (
                      <Link
                        href={`/events/${event.slug.current}`}
                        className="mt-4 inline-block bg-primary-600 text-white px-4 py-2 rounded-md font-semibold hover:bg-primary-700 transition-colors duration-300"
                      >
                        View Details →
                      </Link>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-neutral-50 rounded-lg p-8 text-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-12 w-12 text-neutral-400 mx-auto mb-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                  />
                </svg>
                <h3 className="text-xl font-bold mb-2">No Upcoming Events</h3>
                <p className="text-neutral-600">
                  Check back soon for our upcoming fixtures and events.
                </p>
              </div>
            )}
          </div>
        </section>

        {/* Past Events Section */}
        <section className="py-16 bg-neutral-50">
          <div className="container">
            <h2 className="text-3xl font-bold mb-8">Past Events</h2>
            
            {pastEvents.length > 0 ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {pastEvents.map((event: Event) => (
                  <div key={event._id} className={`
                    bg-neutral-100 border border-neutral-300 rounded-lg p-8 shadow-md
                    hover:scale-105 transform transition-all duration-300 ease-out
                    border-t-4 ${
                      event.eventType === "match" ? "border-t-primary-400" :
                      event.eventType === "training" ? "border-t-secondary-400" :
                      "border-t-neutral-400"
                    }
                  `}>
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="text-xl font-bold mb-2">{event.title}</h3>
                        <p className="text-neutral-600 mb-1">
                          {new Date(event.date).toLocaleDateString("en-US", {
                            weekday: "long",
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          })}
                        </p>
                        {event.location && <p className="text-neutral-600 mb-2">{event.location}</p>}
                      </div>
                      {event.eventType && (
                        <span className={`text-sm px-3 py-1.5 rounded uppercase font-bold ${
                          event.eventType === "match" 
                            ? "bg-primary-100 text-primary-800" 
                            : event.eventType === "training" 
                            ? "bg-secondary-100 text-secondary-800"
                            : "bg-neutral-100 text-neutral-800"
                        }`}>
                          {event.eventType}
                        </span>
                      )}
                    </div>

                    {event.eventType === "match" && (
                      <div className="mt-3">
                        <p className="text-neutral-800">
                          <span className="font-medium">
                            {event.homeOrAway === "Home" ? "Home game vs " : "Away game @ "}
                            {event.opponent}
                          </span>
                          {event.result && (
                            <span className="ml-2 text-neutral-600">
                              ({event.result})
                            </span>
                          )}
                        </p>
                      </div>
                    )}

                    {event.slug && (
                      <Link
                        href={`/events/${event.slug.current}`}
                        className="mt-4 inline-block bg-primary-600 text-white px-4 py-2 rounded-md font-semibold hover:bg-primary-700 transition-colors duration-300"
                      >
                        View Details →
                      </Link>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <div className="bg-white rounded-lg p-8 text-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-12 w-12 text-neutral-400 mx-auto mb-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <h3 className="text-xl font-bold mb-2">No Past Events</h3>
                <p className="text-neutral-600">
                  Our event history will appear here after events have taken place.
                </p>
              </div>
            )}
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
} 