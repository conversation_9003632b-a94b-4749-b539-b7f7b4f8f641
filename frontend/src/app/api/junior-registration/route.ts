import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Parse the request body
    const formData = await request.json();
    
    // Basic validation
    if (!formData.childName || !formData.childDob || !formData.parentName || !formData.email || !formData.phone || !formData.emergencyContact) {
      return NextResponse.json(
        { error: 'Missing required fields' },
        { status: 400 }
      );
    }
    
    // In a real application, you would:
    // 1. Save the data to a database
    // 2. Send confirmation emails
    // 3. Possibly integrate with payment systems
    
    // For now, let's just log the data and return a success response
    console.log('Registration received:', formData);
    
    // Simulate processing time
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Return success response
    return NextResponse.json({ 
      success: true, 
      message: 'Registration submitted successfully! We will contact you soon with further details.'
    });
    
  } catch (error) {
    console.error('Registration error:', error);
    
    return NextResponse.json(
      { error: 'Failed to process registration' },
      { status: 500 }
    );
  }
} 