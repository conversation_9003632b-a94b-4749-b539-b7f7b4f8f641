'use client';

import Header from "@/components/Header";
import Footer from "@/components/Footer";
import Image from "next/image";
import Link from "next/link";
import dynamic from 'next/dynamic';

const ParticleSection = dynamic(() => import('@/components/ParticleSection'), { ssr: false });
const DirectRegistrationForm = dynamic(() => import('../../../components/DirectRegistrationForm'), { ssr: false });

export default function JuniorAcademyClientContent() {
  return (
    <>
      <Header />
      <main>
        {/* Hero Section */}
        <section className="relative min-h-[60vh] flex items-center">
          {/* Background Image */}
          <div className="absolute inset-0 z-0">
            <Image
              src="/junior-academy.jpg"
              alt="Children playing football"
              fill
              priority
              className="object-cover brightness-75"
            />
          </div>
          
          {/* Overlay for better text contrast */}
          <div className="absolute inset-0 bg-gradient-to-r from-black/50 via-black/30 to-transparent z-1"></div>
          
          <div className="container relative z-10 text-white">
            <div className="max-w-2xl">
              <h1 className="text-4xl md:text-5xl font-bold mb-4 leading-tight">
                Junior Academy
              </h1>
              <p className="text-xl mb-6">
                Developing the next generation of football stars through fun, 
                engaging, and professional coaching.
              </p>
              <Link
                href="#register"
                className="bg-yellow-400 text-primary-900 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors shadow-lg inline-flex items-center"
              >
                Register Now
                <svg className="w-5 h-5 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14 5l7 7m0 0l-7 7m7-7H3"></path>
                </svg>
              </Link>
            </div>
          </div>
        </section>

        {/* Program Overview Section */}
        <section className="py-16">
          <ParticleSection className="container">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
              <div>
                <h2 className="text-3xl font-bold mb-6">Program Overview</h2>
                <p className="mb-4">
                  The Northern Nepalese United FC Junior Academy is designed to introduce children 
                  ages 5-15 to the beautiful game of football in a fun, safe, and supportive environment. 
                  Our program focuses on developing fundamental skills, fostering teamwork, and building 
                  confidence both on and off the pitch.
                </p>
                <p>
                  Led by qualified coaches with experience in youth development, our structured training 
                  sessions are tailored to different age groups and skill levels, ensuring every child 
                  can progress at their own pace while enjoying the game.
                </p>
              </div>
              <div className="relative h-80 rounded-lg overflow-hidden shadow-lg">
                <Image
                  src="/junior-training.jpg"
                  alt="Junior football training session"
                  fill
                  className="object-cover"
                />
              </div>
            </div>
          </ParticleSection>
        </section>

        {/* Program Details Section */}
        <section className="py-16 bg-neutral-50">
          <div className="container">
            <h2 className="text-3xl font-bold mb-12 text-center">Program Details</h2>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {/* Age Groups */}
              <div className="bg-white p-8 rounded-lg shadow-md">
                <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mb-6 mx-auto">
                  <svg className="w-8 h-8 text-primary-800" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-4 text-center">Age Groups</h3>
                <ul className="space-y-2">
                  <li className="flex items-center">
                    <span className="bg-primary-100 text-primary-800 rounded-full p-1 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </span>
                    <span>Little Stars: Ages 5-7</span>
                  </li>
                  <li className="flex items-center">
                    <span className="bg-primary-100 text-primary-800 rounded-full p-1 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </span>
                    <span>Junior Developers: Ages 8-11</span>
                  </li>
                  <li className="flex items-center">
                    <span className="bg-primary-100 text-primary-800 rounded-full p-1 mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </span>
                    <span>Youth Talents: Ages 12-15</span>
                  </li>
                </ul>
              </div>

              {/* Training Schedule */}
              <div className="bg-white p-8 rounded-lg shadow-md">
                <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mb-6 mx-auto">
                  <svg className="w-8 h-8 text-primary-800" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-4 text-center">Training Schedule</h3>
                <ul className="space-y-3">
                  <li className="mb-2">
                    <p className="font-semibold text-primary-700">Little Stars (5-7):</p>
                    <p>Saturdays, 9:00 AM - 10:00 AM</p>
                  </li>
                  <li className="mb-2">
                    <p className="font-semibold text-primary-700">Junior Developers (8-11):</p>
                    <p>Saturdays, 10:15 AM - 11:30 AM</p>
                  </li>
                  <li>
                    <p className="font-semibold text-primary-700">Youth Talents (12-15):</p>
                    <p>Saturdays, 11:45 AM - 1:15 PM</p>
                  </li>
                </ul>
              </div>

              {/* Location */}
              <div className="bg-white p-8 rounded-lg shadow-md">
                <div className="bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mb-6 mx-auto">
                  <svg className="w-8 h-8 text-primary-800" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                </div>
                <h3 className="text-xl font-bold mb-4 text-center">Location</h3>
                <p className="mb-4 text-center">
                  John Oxley Reserve Field 2
                  <br />
                  2 Ogg Rd, Murrumba Downs QLD 4503
                </p>
                <div className="text-center">
                  <a
                    href="https://maps.google.com/?q=2+Ogg+Rd,+Murrumba+Downs+QLD+4503"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="inline-flex items-center text-primary-600 font-semibold hover:text-primary-800 transition-colors"
                  >
                    View on Map
                    <svg className="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                    </svg>
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Benefits Section */}
        <section className="py-16">
          <div className="container">
            <h2 className="text-3xl font-bold mb-12 text-center">Benefits of Joining Our Academy</h2>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto">
              <div className="bg-white border border-gray-100 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <div className="flex items-start">
                  <div className="bg-primary-100 p-3 rounded-full mr-4">
                    <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">Skill Development</h3>
                    <p className="text-gray-600">
                      Structured training focusing on technical skills such as dribbling, passing, shooting, and game awareness.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white border border-gray-100 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <div className="flex items-start">
                  <div className="bg-primary-100 p-3 rounded-full mr-4">
                    <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">Teamwork & Social Skills</h3>
                    <p className="text-gray-600">
                      Learning to work with others, communicate effectively, and build lasting friendships.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white border border-gray-100 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <div className="flex items-start">
                  <div className="bg-primary-100 p-3 rounded-full mr-4">
                    <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">Physical Health</h3>
                    <p className="text-gray-600">
                      Regular exercise that improves cardiovascular health, coordination, strength, and overall fitness.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-white border border-gray-100 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow">
                <div className="flex items-start">
                  <div className="bg-primary-100 p-3 rounded-full mr-4">
                    <svg className="w-6 h-6 text-primary-600" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                    </svg>
                  </div>
                  <div>
                    <h3 className="text-xl font-bold mb-2">Confidence Building</h3>
                    <p className="text-gray-600">
                      Developing self-esteem through mastering new skills and overcoming challenges in a supportive environment.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Coaches Section */}
        <section className="py-16 bg-primary-900 text-white">
          <div className="container">
            <h2 className="text-3xl font-bold mb-12 text-center">Our Coaching Team</h2>

            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8 max-w-5xl mx-auto">
              {/* Coach 1 */}
              <div className="bg-primary-800/60 p-6 rounded-xl text-center">
                <div className="w-32 h-32 rounded-full bg-neutral-200 mx-auto mb-4 relative overflow-hidden">
                  <div className="absolute inset-0 flex items-center justify-center text-neutral-500">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold">Raj Sharma</h3>
                <p className="text-yellow-400 font-medium mb-2">Head Coach</p>
                <p className="text-gray-300 text-sm">
                  FA Level 2 Coach with 10+ years experience in youth development
                </p>
              </div>

              {/* Coach 2 */}
              <div className="bg-primary-800/60 p-6 rounded-xl text-center">
                <div className="w-32 h-32 rounded-full bg-neutral-200 mx-auto mb-4 relative overflow-hidden">
                  <div className="absolute inset-0 flex items-center justify-center text-neutral-500">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold">Sita Gurung</h3>
                <p className="text-yellow-400 font-medium mb-2">Youth Coach</p>
                <p className="text-gray-300 text-sm">
                  Specialized in coaching children aged 5-11, with background in early childhood education
                </p>
              </div>

              {/* Coach 3 */}
              <div className="bg-primary-800/60 p-6 rounded-xl text-center">
                <div className="w-32 h-32 rounded-full bg-neutral-200 mx-auto mb-4 relative overflow-hidden">
                  <div className="absolute inset-0 flex items-center justify-center text-neutral-500">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  </div>
                </div>
                <h3 className="text-xl font-bold">Anil Thapa</h3>
                <p className="text-yellow-400 font-medium mb-2">Technical Coach</p>
                <p className="text-gray-300 text-sm">
                  Former professional player focusing on advanced skills for older youth players
                </p>
              </div>
            </div>

            <div className="mt-12 text-center">
              <p className="text-lg max-w-3xl mx-auto">
                All our coaches hold current Working with Children Checks and First Aid certifications. They are passionate about youth development both on and off the pitch.
              </p>
            </div>
          </div>
        </section>

        {/* FAQ Section */}
        <section className="py-16 bg-neutral-50">
          <div className="container">
            <h2 className="text-3xl font-bold mb-12 text-center">Frequently Asked Questions</h2>

            <div className="max-w-3xl mx-auto space-y-6">
              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-bold mb-3">What equipment does my child need?</h3>
                <p className="text-gray-700">
                  Players should wear comfortable sports clothes, shin guards, and football boots (sneakers are acceptable for the youngest age group). Each player should bring their own water bottle. Training bibs and balls are provided by the club.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-bold mb-3">Do you offer trial sessions?</h3>
                <p className="text-gray-700">
                  Yes, new players are welcome to attend one free trial session before committing to the program. Please contact us in advance to arrange this.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-bold mb-3">What happens if it rains?</h3>
                <p className="text-gray-700">
                  In case of light rain, training will usually continue. For heavy rain or unsafe conditions, sessions may be canceled. We will notify parents via email and SMS as early as possible.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-bold mb-3">Are parents required to stay during training?</h3>
                <p className="text-gray-700">
                  Parents of children under 8 are required to remain at the venue during sessions. For older children, parents may leave but must be contactable by phone and return promptly at the end of the session.
                </p>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm">
                <h3 className="text-xl font-bold mb-3">How much does it cost?</h3>
                <p className="text-gray-700">
                  The program costs $150 per term (10 weeks) with discounts available for siblings. This includes weekly training sessions, a club t-shirt, and end-of-term participation certificate.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Registration Section */}
        <section id="register" className="py-16 scroll-mt-24">
          <div className="container">
            <div className="bg-white rounded-xl shadow-lg overflow-hidden max-w-5xl mx-auto">
              <div className="grid grid-cols-1 md:grid-cols-2">
                <div className="bg-primary-800 p-8 md:p-12 text-white">
                  <h2 className="text-3xl font-bold mb-6">Register Your Child</h2>
                  <p className="mb-4">
                    Join our Junior Academy today and give your child the opportunity to develop football skills in a fun, supportive environment.
                  </p>
                  <ul className="space-y-3 mb-8">
                    <li className="flex items-center">
                      <svg className="w-5 h-5 mr-3 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      10-week training programs
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 mr-3 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Professional coaching staff
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 mr-3 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Age-appropriate training methods
                    </li>
                    <li className="flex items-center">
                      <svg className="w-5 h-5 mr-3 text-yellow-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                      </svg>
                      Free club t-shirt included
                    </li>
                  </ul>
                  <p className="text-sm text-gray-300">
                    Next term starts: February 3, 2024
                  </p>
                </div>
                <div className="p-8 md:p-12">
                  <h3 className="text-2xl font-bold mb-6 text-primary-800">Registration Form</h3>
                  <DirectRegistrationForm />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Call to Action Section */}
        <section className="bg-gradient-to-r from-primary-800 to-primary-900 py-10">
          <div className="container">
            <div className="flex flex-col md:flex-row items-center justify-between max-w-5xl mx-auto">
              <div className="text-white mb-6 md:mb-0 text-center md:text-left">
                <h2 className="text-2xl font-bold mb-2">Have Questions?</h2>
                <p className="text-sm text-gray-300 max-w-xl">
                  Contact our Junior Academy coordinator for more information about our programs.
                </p>
              </div>
              <div className="flex gap-3">
                <Link
                  href="/contact"
                  className="bg-white text-primary-800 px-5 py-2 rounded-lg font-semibold hover:bg-yellow-400 transition-colors shadow-md text-sm"
                >
                  Contact Us
                </Link>
                <a
                  href="tel:+61412345678"
                  className="bg-transparent border-2 border-white text-white px-5 py-2 rounded-lg font-semibold hover:bg-white hover:text-primary-800 transition-colors text-sm"
                >
                  Call: 0412 345 678
                </a>
              </div>
            </div>
          </div>
        </section>
      </main>
      <Footer />
    </>
  );
}