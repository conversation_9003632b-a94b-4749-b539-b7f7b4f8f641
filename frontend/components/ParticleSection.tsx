'use client';

import React, { useState, useEffect } from 'react';
import Particles from '@tsparticles/react';
import type { ISourceOptions } from '@tsparticles/engine';
// import { loadLinksPreset } from 'tsparticles-preset-links'; // A common preset - Removing to simplify

interface ParticleSectionProps {
  children: React.ReactNode;
  className?: string;
  particlesOptions?: ISourceOptions; // Allow custom options
}

const ParticleSection: React.FC<ParticleSectionProps> = ({
  children,
  className = '',
  particlesOptions,
}) => {
  const [isMounted, setIsMounted] = useState(false);

  useEffect(() => {
    setIsMounted(true);
  }, []);

  const defaultOptions: ISourceOptions = {
    // preset: 'links', // Removing preset and defining manually 
    background: {
      color: {
        value: 'transparent', 
      },
    },
    particles: {
      color: {
        value: '#cccccc', 
      },
      links: {
        color: '#dddddd', 
        distance: 150,
        enable: true,
        opacity: 0.2, 
        width: 1,
      },
      move: {
        direction: 'none',
        enable: true,
        outModes: {
          default: 'bounce',
        },
        random: false,
        speed: 0.5, 
        straight: false,
      },
      number: {
        density: {
          enable: true,
          // area: 1000, // 'area' might be deprecated or part of the preset
        },
        value: 30, 
      },
      opacity: {
        value: 0.3, // Make particles subtle
      },
      shape: {
        type: 'circle',
      },
      size: {
        value: { min: 1, max: 3 }, // Small particles
      },
    },
    detectRetina: true,
  };

  const options = particlesOptions || defaultOptions;

  return (
    <div className={`relative ${className}`}>
      {isMounted && (
        <Particles
          id={`tsparticles-${Math.random().toString(36).substring(7)}`} // Unique ID for each instance
          options={options}
          className="absolute inset-0 z-0" // Position behind content
        />
      )}
      <div className="relative z-10">{children}</div> {/* Content on top */}
    </div>
  );
};

export default ParticleSection;