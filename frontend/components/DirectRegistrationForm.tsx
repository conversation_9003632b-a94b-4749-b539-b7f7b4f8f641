'use client';

import { useState, FormEvent, ChangeEvent } from 'react';

interface FormValues {
  childName: string;
  childDob: string;
  ageGroup: string;
  parentName: string;
  email: string;
  phone: string;
  emergencyContact: string;
  medicalInfo: string;
  photoConsent: boolean;
}

interface SubmitStatus {
  success?: boolean;
  message?: string;
}

export default function DirectRegistrationForm() {
  const [formData, setFormData] = useState<FormValues>({
    childName: '',
    childDob: '',
    ageGroup: '',
    parentName: '',
    email: '',
    phone: '',
    emergencyContact: '',
    medicalInfo: '',
    photoConsent: false,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<SubmitStatus | null>(null);
  const [errors, setErrors] = useState<Partial<FormValues>>({});

  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = type === 'checkbox' ? (e.target as HTMLInputElement).checked : undefined;

    console.log('Input change:', { name, value, type, checked }); // Debug log

    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));

    // Clear any existing error for this field
    if (errors[name as keyof FormValues]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<FormValues> = {};

    if (!formData.childName.trim()) newErrors.childName = 'Child name is required';
    if (!formData.childDob) newErrors.childDob = 'Date of birth is required';
    if (!formData.ageGroup) newErrors.ageGroup = 'Age group is required';
    if (!formData.parentName.trim()) newErrors.parentName = 'Parent/Guardian name is required';
    if (!formData.email.trim()) newErrors.email = 'Email is required';
    if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';
    if (!formData.emergencyContact.trim()) newErrors.emergencyContact = 'Emergency contact is required';

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus(null);

    try {
      const response = await fetch('/api/junior-registration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || 'Failed to submit registration');
      }

      setSubmitStatus({
        success: true,
        message: data.message || 'Registration submitted successfully! We will contact you soon with further details.'
      });

      // Reset form after successful submission
      setFormData({
        childName: '',
        childDob: '',
        ageGroup: '',
        parentName: '',
        email: '',
        phone: '',
        emergencyContact: '',
        medicalInfo: '',
        photoConsent: false,
      });

    } catch (error) {
      console.error('Registration error:', error);
      setSubmitStatus({
        success: false,
        message: error instanceof Error ? error.message : 'Failed to submit registration. Please try again.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      {/* Success/Error Messages */}
      {submitStatus && (
        <div className={`mb-6 p-4 rounded-lg ${
          submitStatus.success
            ? 'bg-green-50 border border-green-200 text-green-800'
            : 'bg-red-50 border border-red-200 text-red-800'
        }`}>
          <div className="flex">
            <div className="flex-shrink-0">
              {submitStatus.success ? (
                <svg className="h-5 w-5 text-green-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
              )}
            </div>
            <div className="ml-3">
              <p className="text-sm font-medium">{submitStatus.message}</p>
            </div>
          </div>
        </div>
      )}

      <form onSubmit={handleSubmit} className="space-y-5">
        <div>
          <label htmlFor="childName" className="block text-sm font-medium text-gray-700 mb-1">
            Child&apos;s Full Name*
          </label>
          <input
            type="text"
            id="childName"
            name="childName"
            value={formData.childName}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 hover:border-gray-400 ${
              errors.childName ? 'border-red-500 bg-red-50' : 'border-gray-300 bg-white'
            }`}
            placeholder="Enter child's full name"
            required
          />
          {errors.childName && <p className="mt-1 text-sm text-red-600">{errors.childName}</p>}
        </div>

        <div>
          <label htmlFor="childDob" className="block text-sm font-medium text-gray-700 mb-1">
            Date of Birth*
          </label>
          <input
            type="date"
            id="childDob"
            name="childDob"
            value={formData.childDob}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 hover:border-gray-400 ${
              errors.childDob ? 'border-red-500 bg-red-50' : 'border-gray-300 bg-white'
            }`}
            required
          />
          {errors.childDob && <p className="mt-1 text-sm text-red-600">{errors.childDob}</p>}
        </div>

        <div>
          <label htmlFor="ageGroup" className="block text-sm font-medium text-gray-700 mb-1">
            Age Group*
          </label>
          <select
            id="ageGroup"
            name="ageGroup"
            value={formData.ageGroup}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 hover:border-gray-400 ${
              errors.ageGroup ? 'border-red-500 bg-red-50' : 'border-gray-300 bg-white'
            }`}
            required
          >
            <option value="">Select age group</option>
            <option value="little-stars">Little Stars (Ages 5-7)</option>
            <option value="junior-developers">Junior Developers (Ages 8-11)</option>
            <option value="youth-talents">Youth Talents (Ages 12-15)</option>
          </select>
          {errors.ageGroup && <p className="mt-1 text-sm text-red-600">{errors.ageGroup}</p>}
        </div>

        <div>
          <label htmlFor="parentName" className="block text-sm font-medium text-gray-700 mb-1">
            Parent/Guardian Name*
          </label>
          <input
            type="text"
            id="parentName"
            name="parentName"
            value={formData.parentName}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 hover:border-gray-400 ${
              errors.parentName ? 'border-red-500 bg-red-50' : 'border-gray-300 bg-white'
            }`}
            placeholder="Enter parent/guardian name"
            required
          />
          {errors.parentName && <p className="mt-1 text-sm text-red-600">{errors.parentName}</p>}
        </div>

        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
            Email Address*
          </label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 hover:border-gray-400 ${
              errors.email ? 'border-red-500 bg-red-50' : 'border-gray-300 bg-white'
            }`}
            placeholder="Enter email address"
            required
          />
          {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
        </div>

        <div>
          <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
            Phone Number*
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            value={formData.phone}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 hover:border-gray-400 ${
              errors.phone ? 'border-red-500 bg-red-50' : 'border-gray-300 bg-white'
            }`}
            placeholder="Enter phone number"
            required
          />
          {errors.phone && <p className="mt-1 text-sm text-red-600">{errors.phone}</p>}
        </div>

        <div>
          <label htmlFor="emergencyContact" className="block text-sm font-medium text-gray-700 mb-1">
            Emergency Contact*
          </label>
          <input
            type="text"
            id="emergencyContact"
            name="emergencyContact"
            value={formData.emergencyContact}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 hover:border-gray-400 ${
              errors.emergencyContact ? 'border-red-500 bg-red-50' : 'border-gray-300 bg-white'
            }`}
            placeholder="Name and phone number"
            required
          />
          {errors.emergencyContact && <p className="mt-1 text-sm text-red-600">{errors.emergencyContact}</p>}
        </div>

        <div>
          <label htmlFor="medicalInfo" className="block text-sm font-medium text-gray-700 mb-1">
            Medical Information (optional)
          </label>
          <textarea
            id="medicalInfo"
            name="medicalInfo"
            value={formData.medicalInfo}
            onChange={handleInputChange}
            placeholder="Please inform us of any medical conditions, allergies, or special needs"
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg transition-colors duration-200 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 hover:border-gray-400 bg-white"
          />
        </div>

        <div className="flex items-start">
          <div className="flex items-center h-5">
            <input
              id="photoConsent"
              name="photoConsent"
              type="checkbox"
              checked={formData.photoConsent}
              onChange={handleInputChange}
              className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-2 focus:ring-primary-500"
            />
          </div>
          <div className="ml-3 text-sm">
            <label htmlFor="photoConsent" className="font-medium text-gray-700 cursor-pointer">
              I consent to the use of photos/videos of my child for club promotional purposes
            </label>
          </div>
        </div>

        <div className="pt-2">
          <button
            type="submit"
            disabled={isSubmitting}
            className={`w-full font-semibold py-3 px-6 rounded-lg transition-all duration-300 ${
              isSubmitting
                ? 'bg-gray-400 cursor-not-allowed text-white'
                : 'bg-primary-600 hover:bg-primary-700 text-white hover:shadow-lg transform hover:-translate-y-0.5'
            }`}
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center">
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Submitting...
              </div>
            ) : (
              'Submit Registration'
            )}
          </button>
        </div>

        <p className="text-sm text-gray-500 text-center mt-4">
          Places are limited and allocated on a first-come, first-served basis.
          <br />
          For questions, email <a href="mailto:<EMAIL>" className="text-primary-600 font-semibold hover:text-primary-700 transition-colors"><EMAIL></a>
        </p>
      </form>
    </div>
  );
}