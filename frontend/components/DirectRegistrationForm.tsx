'use client';

import { useState, FormEvent, ChangeEvent } from 'react';

interface FormValues {
  childName: string;
  childDob: string;
  ageGroup: string;
  parentName: string;
  email: string;
  phone: string;
  emergencyContact: string;
  medicalInfo: string;
  photoConsent: boolean;
}

export default function DirectRegistrationForm() {
  const [formData, setFormData] = useState<FormValues>({
    childName: '',
    childDob: '',
    ageGroup: '',
    parentName: '',
    email: '',
    phone: '',
    emergencyContact: '',
    medicalInfo: '',
    photoConsent: false,
  });

  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    const checked = type === 'checkbox' ? (e.target as HTMLInputElement).checked : undefined;
    
    setFormData({
      ...formData,
      [name]: type === 'checkbox' ? checked : value
    });
  };

  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    alert('Form submitted!');
    // Basic validation and submission logic would go here
  };

  return (
    <div>
      <form onSubmit={handleSubmit} className="space-y-5">
        <div>
          <label htmlFor="childName" className="block text-sm font-medium text-gray-700 mb-1">
            Child&apos;s Full Name*
          </label>
          <input
            type="text"
            id="childName"
            name="childName"
            value={formData.childName}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border rounded-lg border-gray-300"
            required
          />
        </div>
        
        <div>
          <label htmlFor="childDob" className="block text-sm font-medium text-gray-700 mb-1">
            Date of Birth*
          </label>
          <input
            type="date"
            id="childDob"
            name="childDob"
            value={formData.childDob}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border rounded-lg border-gray-300"
            required
          />
        </div>
        
        <div>
          <label htmlFor="ageGroup" className="block text-sm font-medium text-gray-700 mb-1">
            Age Group*
          </label>
          <select
            id="ageGroup"
            name="ageGroup"
            value={formData.ageGroup}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border rounded-lg border-gray-300"
            required
          >
            <option value="">Select age group</option>
            <option value="little-stars">Little Stars (Ages 5-7)</option>
            <option value="junior-developers">Junior Developers (Ages 8-11)</option>
            <option value="youth-talents">Youth Talents (Ages 12-15)</option>
          </select>
        </div>
        
        <div>
          <label htmlFor="parentName" className="block text-sm font-medium text-gray-700 mb-1">
            Parent/Guardian Name*
          </label>
          <input
            type="text"
            id="parentName"
            name="parentName"
            value={formData.parentName}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border rounded-lg border-gray-300"
            required
          />
        </div>
        
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
            Email Address*
          </label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border rounded-lg border-gray-300"
            required
          />
        </div>
        
        <div>
          <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
            Phone Number*
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            value={formData.phone}
            onChange={handleInputChange}
            className="w-full px-3 py-2 border rounded-lg border-gray-300"
            required
          />
        </div>
        
        <div>
          <label htmlFor="emergencyContact" className="block text-sm font-medium text-gray-700 mb-1">
            Emergency Contact*
          </label>
          <input
            type="text"
            id="emergencyContact"
            name="emergencyContact"
            value={formData.emergencyContact}
            onChange={handleInputChange}
            placeholder="Name and phone number"
            className="w-full px-3 py-2 border rounded-lg border-gray-300"
            required
          />
        </div>
        
        <div>
          <label htmlFor="medicalInfo" className="block text-sm font-medium text-gray-700 mb-1">
            Medical Information (optional)
          </label>
          <textarea
            id="medicalInfo"
            name="medicalInfo"
            value={formData.medicalInfo}
            onChange={handleInputChange}
            placeholder="Please inform us of any medical conditions, allergies, or special needs"
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg"
          />
        </div>
        
        <div className="flex items-start">
          <div className="flex items-center h-5">
            <input
              id="photoConsent"
              name="photoConsent"
              type="checkbox"
              checked={formData.photoConsent}
              onChange={handleInputChange}
              className="w-4 h-4 text-primary-600 border-gray-300 rounded"
            />
          </div>
          <div className="ml-3 text-sm">
            <label htmlFor="photoConsent" className="font-medium text-gray-700">
              I consent to the use of photos/videos of my child for club promotional purposes
            </label>
          </div>
        </div>
        
        <div className="pt-2">
          <button
            type="submit"
            className="w-full bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-300"
          >
            Submit Registration
          </button>
        </div>
        
        <p className="text-sm text-gray-500 text-center mt-4">
          Places are limited and allocated on a first-come, first-served basis.
          <br />
          For questions, email <a href="mailto:<EMAIL>" className="text-primary-600 font-semibold"><EMAIL></a>
        </p>
      </form>
    </div>
  );
} 