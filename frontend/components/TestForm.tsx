'use client';

import { useState } from 'react';

export default function TestForm() {
  const [testValue, setTestValue] = useState('');

  return (
    <div className="p-4 border border-red-500 bg-red-50">
      <h3 className="text-lg font-bold mb-4 text-red-800">TEST FORM - Debug</h3>
      <div className="space-y-4">
        <div>
          <label htmlFor="test-input" className="block text-sm font-medium text-gray-700 mb-1">
            Test Input (should be editable):
          </label>
          <input
            type="text"
            id="test-input"
            value={testValue}
            onChange={(e) => {
              console.log('Test input changed:', e.target.value);
              setTestValue(e.target.value);
            }}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            placeholder="Type here to test..."
          />
          <p className="text-sm text-gray-600 mt-1">Current value: "{testValue}"</p>
        </div>
        
        <div>
          <button
            type="button"
            onClick={() => {
              console.log('Button clicked, current value:', testValue);
              alert(`Current value: ${testValue}`);
            }}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Test Button
          </button>
        </div>
      </div>
    </div>
  );
}
