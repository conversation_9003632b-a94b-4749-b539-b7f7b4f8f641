'use client';

import { useState, FormEvent, ChangeEvent } from 'react';

interface FormData {
  childName: string;
  childDob: string;
  ageGroup: string;
  parentName: string;
  email: string;
  phone: string;
  emergencyContact: string;
  medicalInfo: string;
  photoConsent: boolean;
}

export default function RegistrationForm() {
  const [formData, setFormData] = useState<FormData>({
    childName: '',
    childDob: '',
    ageGroup: '',
    parentName: '',
    email: '',
    phone: '',
    emergencyContact: '',
    medicalInfo: '',
    photoConsent: false,
  });
  
  const [errors, setErrors] = useState<Partial<FormData>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<{ success?: boolean; message?: string } | null>(null);

  // Simplified change handler
  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target;
    
    if (type === 'checkbox') {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData({
        ...formData,
        [name]: checked
      });
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
    
    // Clear error when field is edited
    if (errors[name as keyof FormData]) {
      setErrors({
        ...errors,
        [name]: undefined
      });
    }
  };
  
  // Separate handler for select elements
  const handleSelectChange = (e: ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    setFormData({
      ...formData,
      [name]: value
    });
    
    // Clear error when field is edited
    if (errors[name as keyof FormData]) {
      setErrors({
        ...errors,
        [name]: undefined
      });
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};
    
    if (!formData.childName.trim()) {
      newErrors.childName = 'Child name is required';
    }
    
    if (!formData.childDob) {
      newErrors.childDob = 'Date of birth is required';
    }
    
    if (!formData.ageGroup) {
      newErrors.ageGroup = 'Please select an age group';
    }
    
    if (!formData.parentName.trim()) {
      newErrors.parentName = 'Parent/guardian name is required';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i.test(formData.email)) {
      newErrors.email = 'Invalid email address';
    }
    
    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    }
    
    if (!formData.emergencyContact.trim()) {
      newErrors.emergencyContact = 'Emergency contact is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }
    
    setIsSubmitting(true);
    setSubmitStatus(null);
    
    try {
      // Submit form data to our API endpoint
      const response = await fetch('/api/junior-registration', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });
      
      const data = await response.json();
      
      if (!response.ok) {
        throw new Error(data.error || 'Failed to submit registration');
      }
      
      setSubmitStatus({
        success: true,
        message: data.message || 'Registration submitted successfully! We will contact you soon with further details.'
      });
      
      // Reset form after successful submission
      setFormData({
        childName: '',
        childDob: '',
        ageGroup: '',
        parentName: '',
        email: '',
        phone: '',
        emergencyContact: '',
        medicalInfo: '',
        photoConsent: false,
      });
      
    } catch (error) {
      console.error('Registration error:', error);
      setSubmitStatus({
        success: false,
        message: 'Something went wrong. Please try again or contact us directly.'
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      {submitStatus && (
        <div className={`mb-6 p-4 rounded-lg ${submitStatus.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>
          {submitStatus.message}
        </div>
      )}
      
      <form onSubmit={handleSubmit} className="space-y-5">
        <div>
          <label htmlFor="childName" className="block text-sm font-medium text-gray-700 mb-1">
            Child&apos;s Full Name*
          </label>
          <input
            type="text"
            id="childName"
            name="childName"
            value={formData.childName}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${errors.childName ? 'border-red-500' : 'border-gray-300'}`}
          />
          {errors.childName && <p className="mt-1 text-sm text-red-600">{errors.childName}</p>}
        </div>
        
        <div>
          <label htmlFor="childDob" className="block text-sm font-medium text-gray-700 mb-1">
            Date of Birth*
          </label>
          <input
            type="date"
            id="childDob"
            name="childDob"
            value={formData.childDob}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${errors.childDob ? 'border-red-500' : 'border-gray-300'}`}
          />
          {errors.childDob && <p className="mt-1 text-sm text-red-600">{errors.childDob}</p>}
        </div>
        
        <div>
          <label htmlFor="ageGroup" className="block text-sm font-medium text-gray-700 mb-1">
            Age Group*
          </label>
          <select
            id="ageGroup"
            name="ageGroup"
            value={formData.ageGroup}
            onChange={handleSelectChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${errors.ageGroup ? 'border-red-500' : 'border-gray-300'}`}
          >
            <option value="">Select age group</option>
            <option value="little-stars">Little Stars (Ages 5-7)</option>
            <option value="junior-developers">Junior Developers (Ages 8-11)</option>
            <option value="youth-talents">Youth Talents (Ages 12-15)</option>
          </select>
          {errors.ageGroup && <p className="mt-1 text-sm text-red-600">{errors.ageGroup}</p>}
        </div>
        
        <div>
          <label htmlFor="parentName" className="block text-sm font-medium text-gray-700 mb-1">
            Parent/Guardian Name*
          </label>
          <input
            type="text"
            id="parentName"
            name="parentName"
            value={formData.parentName}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${errors.parentName ? 'border-red-500' : 'border-gray-300'}`}
          />
          {errors.parentName && <p className="mt-1 text-sm text-red-600">{errors.parentName}</p>}
        </div>
        
        <div>
          <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
            Email Address*
          </label>
          <input
            type="email"
            id="email"
            name="email"
            value={formData.email}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${errors.email ? 'border-red-500' : 'border-gray-300'}`}
          />
          {errors.email && <p className="mt-1 text-sm text-red-600">{errors.email}</p>}
        </div>
        
        <div>
          <label htmlFor="phone" className="block text-sm font-medium text-gray-700 mb-1">
            Phone Number*
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            value={formData.phone}
            onChange={handleInputChange}
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${errors.phone ? 'border-red-500' : 'border-gray-300'}`}
          />
          {errors.phone && <p className="mt-1 text-sm text-red-600">{errors.phone}</p>}
        </div>
        
        <div>
          <label htmlFor="emergencyContact" className="block text-sm font-medium text-gray-700 mb-1">
            Emergency Contact*
          </label>
          <input
            type="text"
            id="emergencyContact"
            name="emergencyContact"
            value={formData.emergencyContact}
            onChange={handleInputChange}
            placeholder="Name and phone number"
            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${errors.emergencyContact ? 'border-red-500' : 'border-gray-300'}`}
          />
          {errors.emergencyContact && <p className="mt-1 text-sm text-red-600">{errors.emergencyContact}</p>}
        </div>
        
        <div>
          <label htmlFor="medicalInfo" className="block text-sm font-medium text-gray-700 mb-1">
            Medical Information (optional)
          </label>
          <textarea
            id="medicalInfo"
            name="medicalInfo"
            value={formData.medicalInfo}
            onChange={handleInputChange}
            placeholder="Please inform us of any medical conditions, allergies, or special needs"
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          />
        </div>
        
        <div className="flex items-start">
          <div className="flex items-center h-5">
            <input
              id="photoConsent"
              name="photoConsent"
              type="checkbox"
              checked={formData.photoConsent}
              onChange={handleInputChange}
              className="w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500"
            />
          </div>
          <div className="ml-3 text-sm">
            <label htmlFor="photoConsent" className="font-medium text-gray-700">
              I consent to the use of photos/videos of my child for club promotional purposes
            </label>
          </div>
        </div>
        
        <div className="pt-2">
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-300 flex items-center justify-center disabled:bg-primary-400"
          >
            {isSubmitting ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
              </>
            ) : (
              'Submit Registration'
            )}
          </button>
        </div>
        
        <p className="text-sm text-gray-500 text-center mt-4">
          Places are limited and allocated on a first-come, first-served basis.
          <br />
          For questions, email <a href="mailto:<EMAIL>" className="text-primary-600 font-semibold"><EMAIL></a>
        </p>
      </form>
    </div>
  );
} 