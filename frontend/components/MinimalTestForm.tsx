'use client';

import React from 'react';

export default function MinimalTestForm() {
  return (
    <div style={{ 
      padding: '20px', 
      border: '3px solid red', 
      backgroundColor: 'yellow',
      margin: '20px 0',
      zIndex: 9999,
      position: 'relative'
    }}>
      <h2 style={{ color: 'red', fontSize: '24px', fontWeight: 'bold' }}>
        MINIMAL TEST - NO REACT STATE
      </h2>
      
      {/* Pure HTML input with no React state */}
      <div style={{ marginBottom: '10px' }}>
        <label style={{ display: 'block', fontWeight: 'bold' }}>
          Pure HTML Input (no React state):
        </label>
        <input 
          type="text" 
          placeholder="Type here - pure HTML"
          style={{
            width: '100%',
            padding: '10px',
            border: '2px solid black',
            backgroundColor: 'white',
            fontSize: '16px',
            pointerEvents: 'auto',
            userSelect: 'text',
            zIndex: 10000
          }}
          onInput={(e) => {
            console.log('Pure HTML input:', (e.target as HTMLInputElement).value);
          }}
        />
      </div>

      {/* Test with inline event handler */}
      <div style={{ marginBottom: '10px' }}>
        <label style={{ display: 'block', fontWeight: 'bold' }}>
          Inline Event Handler:
        </label>
        <input 
          type="text" 
          placeholder="Type here - inline handler"
          style={{
            width: '100%',
            padding: '10px',
            border: '2px solid blue',
            backgroundColor: 'white',
            fontSize: '16px'
          }}
          onChange={(e) => {
            console.log('Inline onChange:', e.target.value);
            alert(`You typed: ${e.target.value}`);
          }}
        />
      </div>

      {/* Test button */}
      <button 
        style={{
          padding: '10px 20px',
          backgroundColor: 'green',
          color: 'white',
          border: 'none',
          fontSize: '16px',
          cursor: 'pointer'
        }}
        onClick={() => {
          console.log('Button clicked!');
          alert('Button works!');
        }}
      >
        Test Button - Click Me
      </button>

      <p style={{ marginTop: '10px', fontSize: '14px' }}>
        If you can't type in these inputs or click the button, there's a fundamental issue with JavaScript or CSS.
      </p>
    </div>
  );
}
