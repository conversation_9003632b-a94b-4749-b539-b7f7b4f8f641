{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { useState, useEffect } from 'react'\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n  const [isScrolled, setIsScrolled] = useState(false)\n\n  // Handle scroll effect for sticky header\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 10) {\n        setIsScrolled(true)\n      } else {\n        setIsScrolled(false)\n      }\n    }\n\n    window.addEventListener('scroll', handleScroll)\n    return () => window.removeEventListener('scroll', handleScroll)\n  }, [])\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen)\n  }\n\n  return (\n    <header\n      className={`bg-white sticky top-0 z-50 transition-all duration-200 ${\n        isScrolled ? 'shadow-lg py-2' : 'shadow-md py-3'\n      }`}\n    >\n      <div className=\"container\">\n        <div className=\"flex justify-between items-center\">\n          <Link href=\"/\" className=\"flex items-center space-x-3\">\n            <div className={`relative transition-all duration-200 ${isScrolled ? 'w-10 h-10' : 'w-12 h-12'}`}>\n              <Image\n                src=\"/logo.png\"\n                alt=\"Northern Nepalese United FC Logo\"\n                fill\n                className=\"object-contain\"\n                priority\n              />\n            </div>\n            <div>\n              <span className=\"text-xl font-bold text-primary-800 hidden sm:inline\">Northern Nepalese United FC</span>\n              <span className=\"text-xl font-bold text-primary-800 sm:hidden\">NNUFC</span>\n            </div>\n          </Link>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden p-2 text-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-400 rounded-lg\"\n            onClick={toggleMenu}\n            aria-label=\"Toggle menu\"\n            aria-expanded={isMenuOpen}\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n              className=\"h-6 w-6\"\n            >\n              {isMenuOpen ? (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              ) : (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              )}\n            </svg>\n          </button>\n\n          {/* Desktop navigation */}\n          <nav className=\"hidden md:flex space-x-1 lg:space-x-6\">\n            <NavLink href=\"/\">Home</NavLink>\n            <NavLink href=\"/about\">About</NavLink>\n            <NavLink href=\"/team\">Team</NavLink>\n            <NavLink href=\"/junior-academy\">Junior Academy</NavLink>\n            <NavLink href=\"/news\">News</NavLink>\n            <NavLink href=\"/events\">Events</NavLink>\n            <NavLink href=\"/sponsors\">Sponsors</NavLink>\n            <NavLink href=\"/gallery\">Gallery</NavLink>\n            <NavLink href=\"/contact\">Contact</NavLink>\n          </nav>\n        </div>\n\n        {/* Mobile navigation - slide down animation */}\n        <div\n          className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${\n            isMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'\n          }`}\n        >\n          <nav className=\"mt-4 space-y-2 border-t border-neutral-200 pt-3\">\n            <MobileNavLink href=\"/\" onClick={() => setIsMenuOpen(false)}>Home</MobileNavLink>\n            <MobileNavLink href=\"/about\" onClick={() => setIsMenuOpen(false)}>About</MobileNavLink>\n            <MobileNavLink href=\"/team\" onClick={() => setIsMenuOpen(false)}>Team</MobileNavLink>\n            <MobileNavLink href=\"/junior-academy\" onClick={() => setIsMenuOpen(false)}>Junior Academy</MobileNavLink>\n            <MobileNavLink href=\"/news\" onClick={() => setIsMenuOpen(false)}>News</MobileNavLink>\n            <MobileNavLink href=\"/events\" onClick={() => setIsMenuOpen(false)}>Events</MobileNavLink>\n            <MobileNavLink href=\"/sponsors\" onClick={() => setIsMenuOpen(false)}>Sponsors</MobileNavLink>\n            <MobileNavLink href=\"/gallery\" onClick={() => setIsMenuOpen(false)}>Gallery</MobileNavLink>\n            <MobileNavLink href=\"/contact\" onClick={() => setIsMenuOpen(false)}>Contact</MobileNavLink>\n          </nav>\n        </div>\n      </div>\n    </header>\n  )\n}\n\n// Desktop Navigation Link Component\nfunction NavLink({ href, children }: { href: string; children: React.ReactNode }) {\n  return (\n    <Link\n      href={href}\n      className=\"px-3 py-2 rounded-lg font-medium text-primary-700 transition-colors hover:bg-primary-50 hover:text-primary-900\"\n    >\n      {children}\n    </Link>\n  )\n}\n\n// Mobile Navigation Link Component\nfunction MobileNavLink({ href, onClick, children }: {\n  href: string;\n  onClick: () => void;\n  children: React.ReactNode\n}) {\n  return (\n    <Link\n      href={href}\n      className=\"block py-2 px-1 text-primary-700 hover:bg-primary-50 hover:text-primary-900 rounded-lg font-medium transition-colors\"\n      onClick={onClick}\n    >\n      {children}\n    </Link>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,IAAI,OAAO,OAAO,GAAG,IAAI;wBACvB,cAAc;oBAChB,OAAO;wBACL,cAAc;oBAChB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,uDAAuD,EACjE,aAAa,mBAAmB,kBAChC;kBAEF,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAW,CAAC,qCAAqC,EAAE,aAAa,cAAc,aAAa;8CAC9F,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,IAAI;wCACJ,WAAU;wCACV,QAAQ;;;;;;;;;;;8CAGZ,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAAsD;;;;;;sDACtE,6LAAC;4CAAK,WAAU;sDAA+C;;;;;;;;;;;;;;;;;;sCAKnE,6LAAC;4BACC,WAAU;4BACV,SAAS;4BACT,cAAW;4BACX,iBAAe;sCAEf,cAAA,6LAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,WAAU;0CAET,2BACC,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;yDAGJ,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;;;;;;sCAOV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAQ,MAAK;8CAAI;;;;;;8CAClB,6LAAC;oCAAQ,MAAK;8CAAS;;;;;;8CACvB,6LAAC;oCAAQ,MAAK;8CAAQ;;;;;;8CACtB,6LAAC;oCAAQ,MAAK;8CAAkB;;;;;;8CAChC,6LAAC;oCAAQ,MAAK;8CAAQ;;;;;;8CACtB,6LAAC;oCAAQ,MAAK;8CAAU;;;;;;8CACxB,6LAAC;oCAAQ,MAAK;8CAAY;;;;;;8CAC1B,6LAAC;oCAAQ,MAAK;8CAAW;;;;;;8CACzB,6LAAC;oCAAQ,MAAK;8CAAW;;;;;;;;;;;;;;;;;;8BAK7B,6LAAC;oBACC,WAAW,CAAC,kEAAkE,EAC5E,aAAa,yBAAyB,qBACtC;8BAEF,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAc,MAAK;gCAAI,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CAC7D,6LAAC;gCAAc,MAAK;gCAAS,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CAClE,6LAAC;gCAAc,MAAK;gCAAQ,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACjE,6LAAC;gCAAc,MAAK;gCAAkB,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CAC3E,6LAAC;gCAAc,MAAK;gCAAQ,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACjE,6LAAC;gCAAc,MAAK;gCAAU,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACnE,6LAAC;gCAAc,MAAK;gCAAY,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACrE,6LAAC;gCAAc,MAAK;gCAAW,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACpE,6LAAC;gCAAc,MAAK;gCAAW,SAAS,IAAM,cAAc;0CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhF;GAjHwB;KAAA;AAmHxB,oCAAoC;AACpC,SAAS,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAA+C;IAC9E,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAU;kBAET;;;;;;AAGP;MATS;AAWT,mCAAmC;AACnC,SAAS,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAI/C;IACC,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAU;QACV,SAAS;kBAER;;;;;;AAGP;MAdS"}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/DirectRegistrationForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, FormEvent, ChangeEvent } from 'react';\n\ninterface FormValues {\n  childName: string;\n  childDob: string;\n  ageGroup: string;\n  parentName: string;\n  email: string;\n  phone: string;\n  emergencyContact: string;\n  medicalInfo: string;\n  photoConsent: boolean;\n}\n\nexport default function DirectRegistrationForm() {\n  const [formData, setFormData] = useState<FormValues>({\n    childName: '',\n    childDob: '',\n    ageGroup: '',\n    parentName: '',\n    email: '',\n    phone: '',\n    emergencyContact: '',\n    medicalInfo: '',\n    photoConsent: false,\n  });\n\n  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value, type } = e.target;\n    const checked = type === 'checkbox' ? (e.target as HTMLInputElement).checked : undefined;\n    \n    setFormData({\n      ...formData,\n      [name]: type === 'checkbox' ? checked : value\n    });\n  };\n\n  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    alert('Form submitted!');\n    // Basic validation and submission logic would go here\n  };\n\n  return (\n    <div>\n      <form onSubmit={handleSubmit} className=\"space-y-5\">\n        <div>\n          <label htmlFor=\"childName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Child&apos;s Full Name*\n          </label>\n          <input\n            type=\"text\"\n            id=\"childName\"\n            name=\"childName\"\n            value={formData.childName}\n            onChange={handleInputChange}\n            className=\"w-full px-3 py-2 border rounded-lg border-gray-300\"\n            required\n          />\n        </div>\n        \n        <div>\n          <label htmlFor=\"childDob\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Date of Birth*\n          </label>\n          <input\n            type=\"date\"\n            id=\"childDob\"\n            name=\"childDob\"\n            value={formData.childDob}\n            onChange={handleInputChange}\n            className=\"w-full px-3 py-2 border rounded-lg border-gray-300\"\n            required\n          />\n        </div>\n        \n        <div>\n          <label htmlFor=\"ageGroup\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Age Group*\n          </label>\n          <select\n            id=\"ageGroup\"\n            name=\"ageGroup\"\n            value={formData.ageGroup}\n            onChange={handleInputChange}\n            className=\"w-full px-3 py-2 border rounded-lg border-gray-300\"\n            required\n          >\n            <option value=\"\">Select age group</option>\n            <option value=\"little-stars\">Little Stars (Ages 5-7)</option>\n            <option value=\"junior-developers\">Junior Developers (Ages 8-11)</option>\n            <option value=\"youth-talents\">Youth Talents (Ages 12-15)</option>\n          </select>\n        </div>\n        \n        <div>\n          <label htmlFor=\"parentName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Parent/Guardian Name*\n          </label>\n          <input\n            type=\"text\"\n            id=\"parentName\"\n            name=\"parentName\"\n            value={formData.parentName}\n            onChange={handleInputChange}\n            className=\"w-full px-3 py-2 border rounded-lg border-gray-300\"\n            required\n          />\n        </div>\n        \n        <div>\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Email Address*\n          </label>\n          <input\n            type=\"email\"\n            id=\"email\"\n            name=\"email\"\n            value={formData.email}\n            onChange={handleInputChange}\n            className=\"w-full px-3 py-2 border rounded-lg border-gray-300\"\n            required\n          />\n        </div>\n        \n        <div>\n          <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Phone Number*\n          </label>\n          <input\n            type=\"tel\"\n            id=\"phone\"\n            name=\"phone\"\n            value={formData.phone}\n            onChange={handleInputChange}\n            className=\"w-full px-3 py-2 border rounded-lg border-gray-300\"\n            required\n          />\n        </div>\n        \n        <div>\n          <label htmlFor=\"emergencyContact\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Emergency Contact*\n          </label>\n          <input\n            type=\"text\"\n            id=\"emergencyContact\"\n            name=\"emergencyContact\"\n            value={formData.emergencyContact}\n            onChange={handleInputChange}\n            placeholder=\"Name and phone number\"\n            className=\"w-full px-3 py-2 border rounded-lg border-gray-300\"\n            required\n          />\n        </div>\n        \n        <div>\n          <label htmlFor=\"medicalInfo\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Medical Information (optional)\n          </label>\n          <textarea\n            id=\"medicalInfo\"\n            name=\"medicalInfo\"\n            value={formData.medicalInfo}\n            onChange={handleInputChange}\n            placeholder=\"Please inform us of any medical conditions, allergies, or special needs\"\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg\"\n          />\n        </div>\n        \n        <div className=\"flex items-start\">\n          <div className=\"flex items-center h-5\">\n            <input\n              id=\"photoConsent\"\n              name=\"photoConsent\"\n              type=\"checkbox\"\n              checked={formData.photoConsent}\n              onChange={handleInputChange}\n              className=\"w-4 h-4 text-primary-600 border-gray-300 rounded\"\n            />\n          </div>\n          <div className=\"ml-3 text-sm\">\n            <label htmlFor=\"photoConsent\" className=\"font-medium text-gray-700\">\n              I consent to the use of photos/videos of my child for club promotional purposes\n            </label>\n          </div>\n        </div>\n        \n        <div className=\"pt-2\">\n          <button\n            type=\"submit\"\n            className=\"w-full bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-300\"\n          >\n            Submit Registration\n          </button>\n        </div>\n        \n        <p className=\"text-sm text-gray-500 text-center mt-4\">\n          Places are limited and allocated on a first-come, first-served basis.\n          <br />\n          For questions, email <a href=\"mailto:<EMAIL>\" className=\"text-primary-600 font-semibold\"><EMAIL></a>\n        </p>\n      </form>\n    </div>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAgBe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QACnD,WAAW;QACX,UAAU;QACV,UAAU;QACV,YAAY;QACZ,OAAO;QACP,OAAO;QACP,kBAAkB;QAClB,aAAa;QACb,cAAc;IAChB;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,MAAM,UAAU,SAAS,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAAG;QAE/E,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;QAC1C;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,MAAM;IACN,sDAAsD;IACxD;IAEA,qBACE,6LAAC;kBACC,cAAA,6LAAC;YAAK,UAAU;YAAc,WAAU;;8BACtC,6LAAC;;sCACC,6LAAC;4BAAM,SAAQ;4BAAY,WAAU;sCAA+C;;;;;;sCAGpF,6LAAC;4BACC,MAAK;4BACL,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,SAAS;4BACzB,UAAU;4BACV,WAAU;4BACV,QAAQ;;;;;;;;;;;;8BAIZ,6LAAC;;sCACC,6LAAC;4BAAM,SAAQ;4BAAW,WAAU;sCAA+C;;;;;;sCAGnF,6LAAC;4BACC,MAAK;4BACL,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,QAAQ;4BACxB,UAAU;4BACV,WAAU;4BACV,QAAQ;;;;;;;;;;;;8BAIZ,6LAAC;;sCACC,6LAAC;4BAAM,SAAQ;4BAAW,WAAU;sCAA+C;;;;;;sCAGnF,6LAAC;4BACC,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,QAAQ;4BACxB,UAAU;4BACV,WAAU;4BACV,QAAQ;;8CAER,6LAAC;oCAAO,OAAM;8CAAG;;;;;;8CACjB,6LAAC;oCAAO,OAAM;8CAAe;;;;;;8CAC7B,6LAAC;oCAAO,OAAM;8CAAoB;;;;;;8CAClC,6LAAC;oCAAO,OAAM;8CAAgB;;;;;;;;;;;;;;;;;;8BAIlC,6LAAC;;sCACC,6LAAC;4BAAM,SAAQ;4BAAa,WAAU;sCAA+C;;;;;;sCAGrF,6LAAC;4BACC,MAAK;4BACL,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,UAAU;4BAC1B,UAAU;4BACV,WAAU;4BACV,QAAQ;;;;;;;;;;;;8BAIZ,6LAAC;;sCACC,6LAAC;4BAAM,SAAQ;4BAAQ,WAAU;sCAA+C;;;;;;sCAGhF,6LAAC;4BACC,MAAK;4BACL,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,KAAK;4BACrB,UAAU;4BACV,WAAU;4BACV,QAAQ;;;;;;;;;;;;8BAIZ,6LAAC;;sCACC,6LAAC;4BAAM,SAAQ;4BAAQ,WAAU;sCAA+C;;;;;;sCAGhF,6LAAC;4BACC,MAAK;4BACL,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,KAAK;4BACrB,UAAU;4BACV,WAAU;4BACV,QAAQ;;;;;;;;;;;;8BAIZ,6LAAC;;sCACC,6LAAC;4BAAM,SAAQ;4BAAmB,WAAU;sCAA+C;;;;;;sCAG3F,6LAAC;4BACC,MAAK;4BACL,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,gBAAgB;4BAChC,UAAU;4BACV,aAAY;4BACZ,WAAU;4BACV,QAAQ;;;;;;;;;;;;8BAIZ,6LAAC;;sCACC,6LAAC;4BAAM,SAAQ;4BAAc,WAAU;sCAA+C;;;;;;sCAGtF,6LAAC;4BACC,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,WAAW;4BAC3B,UAAU;4BACV,aAAY;4BACZ,MAAM;4BACN,WAAU;;;;;;;;;;;;8BAId,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,MAAK;gCACL,SAAS,SAAS,YAAY;gCAC9B,UAAU;gCACV,WAAU;;;;;;;;;;;sCAGd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,SAAQ;gCAAe,WAAU;0CAA4B;;;;;;;;;;;;;;;;;8BAMxE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;8BAKH,6LAAC;oBAAE,WAAU;;wBAAyC;sCAEpD,6LAAC;;;;;wBAAK;sCACe,6LAAC;4BAAE,MAAK;4BAA8B,WAAU;sCAAiC;;;;;;;;;;;;;;;;;;;;;;;AAKhH;GAhMwB;KAAA"}}, {"offset": {"line": 801, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}