{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { useState, useEffect } from 'react'\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n  const [isScrolled, setIsScrolled] = useState(false)\n\n  // Handle scroll effect for sticky header\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 10) {\n        setIsScrolled(true)\n      } else {\n        setIsScrolled(false)\n      }\n    }\n\n    window.addEventListener('scroll', handleScroll)\n    return () => window.removeEventListener('scroll', handleScroll)\n  }, [])\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen)\n  }\n\n  return (\n    <header\n      className={`bg-white sticky top-0 z-50 transition-all duration-200 ${\n        isScrolled ? 'shadow-lg py-2' : 'shadow-md py-3'\n      }`}\n    >\n      <div className=\"container\">\n        <div className=\"flex justify-between items-center\">\n          <Link href=\"/\" className=\"flex items-center space-x-3\">\n            <div className={`relative transition-all duration-200 ${isScrolled ? 'w-10 h-10' : 'w-12 h-12'}`}>\n              <Image\n                src=\"/logo.png\"\n                alt=\"Northern Nepalese United FC Logo\"\n                fill\n                className=\"object-contain\"\n                priority\n              />\n            </div>\n            <div>\n              <span className=\"text-xl font-bold text-primary-800 hidden sm:inline\">Northern Nepalese United FC</span>\n              <span className=\"text-xl font-bold text-primary-800 sm:hidden\">NNUFC</span>\n            </div>\n          </Link>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden p-2 text-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-400 rounded-lg\"\n            onClick={toggleMenu}\n            aria-label=\"Toggle menu\"\n            aria-expanded={isMenuOpen}\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n              className=\"h-6 w-6\"\n            >\n              {isMenuOpen ? (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              ) : (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              )}\n            </svg>\n          </button>\n\n          {/* Desktop navigation */}\n          <nav className=\"hidden md:flex space-x-1 lg:space-x-6\">\n            <NavLink href=\"/\">Home</NavLink>\n            <NavLink href=\"/about\">About</NavLink>\n            <NavLink href=\"/team\">Team</NavLink>\n            <NavLink href=\"/junior-academy\">Junior Academy</NavLink>\n            <NavLink href=\"/news\">News</NavLink>\n            <NavLink href=\"/events\">Events</NavLink>\n            <NavLink href=\"/sponsors\">Sponsors</NavLink>\n            <NavLink href=\"/gallery\">Gallery</NavLink>\n            <NavLink href=\"/contact\">Contact</NavLink>\n          </nav>\n        </div>\n\n        {/* Mobile navigation - slide down animation */}\n        <div\n          className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${\n            isMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'\n          }`}\n        >\n          <nav className=\"mt-4 space-y-2 border-t border-neutral-200 pt-3\">\n            <MobileNavLink href=\"/\" onClick={() => setIsMenuOpen(false)}>Home</MobileNavLink>\n            <MobileNavLink href=\"/about\" onClick={() => setIsMenuOpen(false)}>About</MobileNavLink>\n            <MobileNavLink href=\"/team\" onClick={() => setIsMenuOpen(false)}>Team</MobileNavLink>\n            <MobileNavLink href=\"/junior-academy\" onClick={() => setIsMenuOpen(false)}>Junior Academy</MobileNavLink>\n            <MobileNavLink href=\"/news\" onClick={() => setIsMenuOpen(false)}>News</MobileNavLink>\n            <MobileNavLink href=\"/events\" onClick={() => setIsMenuOpen(false)}>Events</MobileNavLink>\n            <MobileNavLink href=\"/sponsors\" onClick={() => setIsMenuOpen(false)}>Sponsors</MobileNavLink>\n            <MobileNavLink href=\"/gallery\" onClick={() => setIsMenuOpen(false)}>Gallery</MobileNavLink>\n            <MobileNavLink href=\"/contact\" onClick={() => setIsMenuOpen(false)}>Contact</MobileNavLink>\n          </nav>\n        </div>\n      </div>\n    </header>\n  )\n}\n\n// Desktop Navigation Link Component\nfunction NavLink({ href, children }: { href: string; children: React.ReactNode }) {\n  return (\n    <Link\n      href={href}\n      className=\"px-3 py-2 rounded-lg font-medium text-primary-700 transition-colors hover:bg-primary-50 hover:text-primary-900\"\n    >\n      {children}\n    </Link>\n  )\n}\n\n// Mobile Navigation Link Component\nfunction MobileNavLink({ href, onClick, children }: {\n  href: string;\n  onClick: () => void;\n  children: React.ReactNode\n}) {\n  return (\n    <Link\n      href={href}\n      className=\"block py-2 px-1 text-primary-700 hover:bg-primary-50 hover:text-primary-900 rounded-lg font-medium transition-colors\"\n      onClick={onClick}\n    >\n      {children}\n    </Link>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,IAAI,OAAO,OAAO,GAAG,IAAI;wBACvB,cAAc;oBAChB,OAAO;wBACL,cAAc;oBAChB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,uDAAuD,EACjE,aAAa,mBAAmB,kBAChC;kBAEF,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAW,CAAC,qCAAqC,EAAE,aAAa,cAAc,aAAa;8CAC9F,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,IAAI;wCACJ,WAAU;wCACV,QAAQ;;;;;;;;;;;8CAGZ,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAAsD;;;;;;sDACtE,6LAAC;4CAAK,WAAU;sDAA+C;;;;;;;;;;;;;;;;;;sCAKnE,6LAAC;4BACC,WAAU;4BACV,SAAS;4BACT,cAAW;4BACX,iBAAe;sCAEf,cAAA,6LAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,WAAU;0CAET,2BACC,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;yDAGJ,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;;;;;;sCAOV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAQ,MAAK;8CAAI;;;;;;8CAClB,6LAAC;oCAAQ,MAAK;8CAAS;;;;;;8CACvB,6LAAC;oCAAQ,MAAK;8CAAQ;;;;;;8CACtB,6LAAC;oCAAQ,MAAK;8CAAkB;;;;;;8CAChC,6LAAC;oCAAQ,MAAK;8CAAQ;;;;;;8CACtB,6LAAC;oCAAQ,MAAK;8CAAU;;;;;;8CACxB,6LAAC;oCAAQ,MAAK;8CAAY;;;;;;8CAC1B,6LAAC;oCAAQ,MAAK;8CAAW;;;;;;8CACzB,6LAAC;oCAAQ,MAAK;8CAAW;;;;;;;;;;;;;;;;;;8BAK7B,6LAAC;oBACC,WAAW,CAAC,kEAAkE,EAC5E,aAAa,yBAAyB,qBACtC;8BAEF,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAc,MAAK;gCAAI,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CAC7D,6LAAC;gCAAc,MAAK;gCAAS,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CAClE,6LAAC;gCAAc,MAAK;gCAAQ,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACjE,6LAAC;gCAAc,MAAK;gCAAkB,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CAC3E,6LAAC;gCAAc,MAAK;gCAAQ,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACjE,6LAAC;gCAAc,MAAK;gCAAU,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACnE,6LAAC;gCAAc,MAAK;gCAAY,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACrE,6LAAC;gCAAc,MAAK;gCAAW,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACpE,6LAAC;gCAAc,MAAK;gCAAW,SAAS,IAAM,cAAc;0CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhF;GAjHwB;KAAA;AAmHxB,oCAAoC;AACpC,SAAS,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAA+C;IAC9E,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAU;kBAET;;;;;;AAGP;MATS;AAWT,mCAAmC;AACnC,SAAS,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAI/C;IACC,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAU;QACV,SAAS;kBAER;;;;;;AAGP;MAdS"}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/ParticleSection.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useCallback } from 'react';\nimport Particles from '@tsparticles/react';\nimport type { Engine, ISourceOptions } from '@tsparticles/engine'; // Changed import source, removed Container\n// import { loadLinksPreset } from 'tsparticles-preset-links'; // A common preset - Removing to simplify\n\ninterface ParticleSectionProps {\n  children: React.ReactNode;\n  className?: string;\n  particlesOptions?: ISourceOptions; // Allow custom options\n}\n\nconst ParticleSection: React.FC<ParticleSectionProps> = ({\n  children,\n  className = '',\n  particlesOptions,\n}) => {\n  const customInit = useCallback(async (_engine: Engine) => {\n    // console.log(engine); // For debugging if needed\n    // Engine is available, presets can be loaded here if needed,\n    // but for now, we'll define all options directly.\n    // await loadLinksPreset(engine); \n  }, []);\n  // const particlesLoaded = useCallback(async (_container?: Container) => { \n  //   // await console.log(_container); \n  // }, []);\n\n  const defaultOptions: ISourceOptions = {\n    // preset: 'links', // Removing preset and defining manually \n    background: {\n      color: {\n        value: 'transparent', \n      },\n    },\n    particles: {\n      color: {\n        value: '#cccccc', \n      },\n      links: {\n        color: '#dddddd', \n        distance: 150,\n        enable: true,\n        opacity: 0.2, \n        width: 1,\n      },\n      move: {\n        direction: 'none',\n        enable: true,\n        outModes: {\n          default: 'bounce',\n        },\n        random: false,\n        speed: 0.5, \n        straight: false,\n      },\n      number: {\n        density: {\n          enable: true,\n          // area: 1000, // 'area' might be deprecated or part of the preset\n        },\n        value: 30, \n      },\n      opacity: {\n        value: 0.3, // Make particles subtle\n      },\n      shape: {\n        type: 'circle',\n      },\n      size: {\n        value: { min: 1, max: 3 }, // Small particles\n      },\n    },\n    detectRetina: true,\n  };\n\n  const options = particlesOptions || defaultOptions;\n\n  return (\n    <div className={`relative ${className}`}>\n      <Particles\n        id={`tsparticles-${Math.random().toString(36).substring(7)}`} // Unique ID for each instance\n        // init={customInit} // Temporarily removed to test type issue\n        // loaded={particlesLoaded} // Use if you need to interact with the container\n        options={options}\n        className=\"absolute inset-0 z-0\" // Position behind content\n      />\n      <div className=\"relative z-10\">{children}</div> {/* Content on top */}\n    </div>\n  );\n};\n\nexport default ParticleSection;"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAaA,MAAM,kBAAkD,CAAC,EACvD,QAAQ,EACR,YAAY,EAAE,EACd,gBAAgB,EACjB;;IACC,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;mDAAE,OAAO;QACpC,kDAAkD;QAClD,6DAA6D;QAC7D,kDAAkD;QAClD,kCAAkC;QACpC;kDAAG,EAAE;IACL,2EAA2E;IAC3E,uCAAuC;IACvC,UAAU;IAEV,MAAM,iBAAiC;QACrC,6DAA6D;QAC7D,YAAY;YACV,OAAO;gBACL,OAAO;YACT;QACF;QACA,WAAW;YACT,OAAO;gBACL,OAAO;YACT;YACA,OAAO;gBACL,OAAO;gBACP,UAAU;gBACV,QAAQ;gBACR,SAAS;gBACT,OAAO;YACT;YACA,MAAM;gBACJ,WAAW;gBACX,QAAQ;gBACR,UAAU;oBACR,SAAS;gBACX;gBACA,QAAQ;gBACR,OAAO;gBACP,UAAU;YACZ;YACA,QAAQ;gBACN,SAAS;oBACP,QAAQ;gBAEV;gBACA,OAAO;YACT;YACA,SAAS;gBACP,OAAO;YACT;YACA,OAAO;gBACL,MAAM;YACR;YACA,MAAM;gBACJ,OAAO;oBAAE,KAAK;oBAAG,KAAK;gBAAE;YAC1B;QACF;QACA,cAAc;IAChB;IAEA,MAAM,UAAU,oBAAoB;IAEpC,qBACE,6LAAC;QAAI,WAAW,CAAC,SAAS,EAAE,WAAW;;0BACrC,6LAAC,8JAAA,CAAA,UAAS;gBACR,IAAI,CAAC,YAAY,EAAE,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,IAAI;gBAC5D,8DAA8D;gBAC9D,6EAA6E;gBAC7E,SAAS;gBACT,WAAU,uBAAuB,0BAA0B;;;;;;0BAE7D,6LAAC;gBAAI,WAAU;0BAAiB;;;;;;YAAe;;;;;;;AAGrD;GA7EM;KAAA;uCA+ES"}}, {"offset": {"line": 496, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 502, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/RegistrationForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, FormEvent, ChangeEvent } from 'react';\n\ninterface FormData {\n  childName: string;\n  childDob: string;\n  ageGroup: string;\n  parentName: string;\n  email: string;\n  phone: string;\n  emergencyContact: string;\n  medicalInfo: string;\n  photoConsent: boolean;\n}\n\nexport default function RegistrationForm() {\n  const [formData, setFormData] = useState<FormData>({\n    childName: '',\n    childDob: '',\n    ageGroup: '',\n    parentName: '',\n    email: '',\n    phone: '',\n    emergencyContact: '',\n    medicalInfo: '',\n    photoConsent: false,\n  });\n  \n  const [errors, setErrors] = useState<Partial<FormData>>({});\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState<{ success?: boolean; message?: string } | null>(null);\n\n  const handleChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value, type } = e.target;\n    const checked = type === 'checkbox' ? (e.target as HTMLInputElement).checked : undefined;\n    \n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n    \n    // Clear error when field is edited\n    if (errors[name as keyof FormData]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: undefined\n      }));\n    }\n  };\n\n  const validateForm = (): boolean => {\n    const newErrors: Partial<FormData> = {};\n    \n    if (!formData.childName.trim()) {\n      newErrors.childName = 'Child name is required';\n    }\n    \n    if (!formData.childDob) {\n      newErrors.childDob = 'Date of birth is required';\n    }\n    \n    if (!formData.ageGroup) {\n      newErrors.ageGroup = 'Please select an age group';\n    }\n    \n    if (!formData.parentName.trim()) {\n      newErrors.parentName = 'Parent/guardian name is required';\n    }\n    \n    if (!formData.email.trim()) {\n      newErrors.email = 'Email is required';\n    } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\\.[A-Z]{2,}$/i.test(formData.email)) {\n      newErrors.email = 'Invalid email address';\n    }\n    \n    if (!formData.phone.trim()) {\n      newErrors.phone = 'Phone number is required';\n    }\n    \n    if (!formData.emergencyContact.trim()) {\n      newErrors.emergencyContact = 'Emergency contact is required';\n    }\n    \n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: FormEvent) => {\n    e.preventDefault();\n    \n    if (!validateForm()) {\n      return;\n    }\n    \n    setIsSubmitting(true);\n    setSubmitStatus(null);\n    \n    try {\n      // Submit form data to our API endpoint\n      const response = await fetch('/api/junior-registration', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      });\n      \n      const data = await response.json();\n      \n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to submit registration');\n      }\n      \n      setSubmitStatus({\n        success: true,\n        message: data.message || 'Registration submitted successfully! We will contact you soon with further details.'\n      });\n      \n      // Reset form after successful submission\n      setFormData({\n        childName: '',\n        childDob: '',\n        ageGroup: '',\n        parentName: '',\n        email: '',\n        phone: '',\n        emergencyContact: '',\n        medicalInfo: '',\n        photoConsent: false,\n      });\n      \n    } catch (error) {\n      console.error('Registration error:', error);\n      setSubmitStatus({\n        success: false,\n        message: 'Something went wrong. Please try again or contact us directly.'\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div>\n      {submitStatus && (\n        <div className={`mb-6 p-4 rounded-lg ${submitStatus.success ? 'bg-green-50 text-green-800' : 'bg-red-50 text-red-800'}`}>\n          {submitStatus.message}\n        </div>\n      )}\n      \n      <form onSubmit={handleSubmit} className=\"space-y-5\">\n        <div>\n          <label htmlFor=\"childName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Child&apos;s Full Name*\n          </label>\n          <input\n            type=\"text\"\n            id=\"childName\"\n            name=\"childName\"\n            value={formData.childName}\n            onChange={handleChange}\n            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${errors.childName ? 'border-red-500' : 'border-gray-300'}`}\n          />\n          {errors.childName && <p className=\"mt-1 text-sm text-red-600\">{errors.childName}</p>}\n        </div>\n        \n        <div>\n          <label htmlFor=\"childDob\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Date of Birth*\n          </label>\n          <input\n            type=\"date\"\n            id=\"childDob\"\n            name=\"childDob\"\n            value={formData.childDob}\n            onChange={handleChange}\n            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${errors.childDob ? 'border-red-500' : 'border-gray-300'}`}\n          />\n          {errors.childDob && <p className=\"mt-1 text-sm text-red-600\">{errors.childDob}</p>}\n        </div>\n        \n        <div>\n          <label htmlFor=\"ageGroup\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Age Group*\n          </label>\n          <select\n            id=\"ageGroup\"\n            name=\"ageGroup\"\n            value={formData.ageGroup}\n            onChange={handleChange}\n            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${errors.ageGroup ? 'border-red-500' : 'border-gray-300'}`}\n          >\n            <option value=\"\">Select age group</option>\n            <option value=\"little-stars\">Little Stars (Ages 5-7)</option>\n            <option value=\"junior-developers\">Junior Developers (Ages 8-11)</option>\n            <option value=\"youth-talents\">Youth Talents (Ages 12-15)</option>\n          </select>\n          {errors.ageGroup && <p className=\"mt-1 text-sm text-red-600\">{errors.ageGroup}</p>}\n        </div>\n        \n        <div>\n          <label htmlFor=\"parentName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Parent/Guardian Name*\n          </label>\n          <input\n            type=\"text\"\n            id=\"parentName\"\n            name=\"parentName\"\n            value={formData.parentName}\n            onChange={handleChange}\n            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${errors.parentName ? 'border-red-500' : 'border-gray-300'}`}\n          />\n          {errors.parentName && <p className=\"mt-1 text-sm text-red-600\">{errors.parentName}</p>}\n        </div>\n        \n        <div>\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Email Address*\n          </label>\n          <input\n            type=\"email\"\n            id=\"email\"\n            name=\"email\"\n            value={formData.email}\n            onChange={handleChange}\n            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${errors.email ? 'border-red-500' : 'border-gray-300'}`}\n          />\n          {errors.email && <p className=\"mt-1 text-sm text-red-600\">{errors.email}</p>}\n        </div>\n        \n        <div>\n          <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Phone Number*\n          </label>\n          <input\n            type=\"tel\"\n            id=\"phone\"\n            name=\"phone\"\n            value={formData.phone}\n            onChange={handleChange}\n            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${errors.phone ? 'border-red-500' : 'border-gray-300'}`}\n          />\n          {errors.phone && <p className=\"mt-1 text-sm text-red-600\">{errors.phone}</p>}\n        </div>\n        \n        <div>\n          <label htmlFor=\"emergencyContact\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Emergency Contact*\n          </label>\n          <input\n            type=\"text\"\n            id=\"emergencyContact\"\n            name=\"emergencyContact\"\n            value={formData.emergencyContact}\n            onChange={handleChange}\n            placeholder=\"Name and phone number\"\n            className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500 ${errors.emergencyContact ? 'border-red-500' : 'border-gray-300'}`}\n          />\n          {errors.emergencyContact && <p className=\"mt-1 text-sm text-red-600\">{errors.emergencyContact}</p>}\n        </div>\n        \n        <div>\n          <label htmlFor=\"medicalInfo\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Medical Information (optional)\n          </label>\n          <textarea\n            id=\"medicalInfo\"\n            name=\"medicalInfo\"\n            value={formData.medicalInfo}\n            onChange={handleChange}\n            placeholder=\"Please inform us of any medical conditions, allergies, or special needs\"\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-primary-500\"\n          />\n        </div>\n        \n        <div className=\"flex items-start\">\n          <div className=\"flex items-center h-5\">\n            <input\n              id=\"photoConsent\"\n              name=\"photoConsent\"\n              type=\"checkbox\"\n              checked={formData.photoConsent}\n              onChange={handleChange}\n              className=\"w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-primary-500\"\n            />\n          </div>\n          <div className=\"ml-3 text-sm\">\n            <label htmlFor=\"photoConsent\" className=\"font-medium text-gray-700\">\n              I consent to the use of photos/videos of my child for club promotional purposes\n            </label>\n          </div>\n        </div>\n        \n        <div className=\"pt-2\">\n          <button\n            type=\"submit\"\n            disabled={isSubmitting}\n            className=\"w-full bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-300 flex items-center justify-center disabled:bg-primary-400\"\n          >\n            {isSubmitting ? (\n              <>\n                <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                </svg>\n                Processing...\n              </>\n            ) : (\n              'Submit Registration'\n            )}\n          </button>\n        </div>\n        \n        <p className=\"text-sm text-gray-500 text-center mt-4\">\n          Places are limited and allocated on a first-come, first-served basis.\n          <br />\n          For questions, email <a href=\"mailto:<EMAIL>\" className=\"text-primary-600 font-semibold\"><EMAIL></a>\n        </p>\n      </form>\n    </div>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAgBe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY;QACjD,WAAW;QACX,UAAU;QACV,UAAU;QACV,YAAY;QACZ,OAAO;QACP,OAAO;QACP,kBAAkB;QAClB,aAAa;QACb,cAAc;IAChB;IAEA,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB,CAAC;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAkD;IAEjG,MAAM,eAAe,CAAC;QACpB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,MAAM,UAAU,SAAS,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAAG;QAE/E,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;QAED,mCAAmC;QACnC,IAAI,MAAM,CAAC,KAAuB,EAAE;YAClC,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,YAA+B,CAAC;QAEtC,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI;YAC9B,UAAU,SAAS,GAAG;QACxB;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB;QAEA,IAAI,CAAC,SAAS,QAAQ,EAAE;YACtB,UAAU,QAAQ,GAAG;QACvB;QAEA,IAAI,CAAC,SAAS,UAAU,CAAC,IAAI,IAAI;YAC/B,UAAU,UAAU,GAAG;QACzB;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB,OAAO,IAAI,CAAC,2CAA2C,IAAI,CAAC,SAAS,KAAK,GAAG;YAC3E,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI;YAC1B,UAAU,KAAK,GAAG;QACpB;QAEA,IAAI,CAAC,SAAS,gBAAgB,CAAC,IAAI,IAAI;YACrC,UAAU,gBAAgB,GAAG;QAC/B;QAEA,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,uCAAuC;YACvC,MAAM,WAAW,MAAM,MAAM,4BAA4B;gBACvD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,gBAAgB;gBACd,SAAS;gBACT,SAAS,KAAK,OAAO,IAAI;YAC3B;YAEA,yCAAyC;YACzC,YAAY;gBACV,WAAW;gBACX,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,OAAO;gBACP,OAAO;gBACP,kBAAkB;gBAClB,aAAa;gBACb,cAAc;YAChB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,gBAAgB;gBACd,SAAS;gBACT,SAAS;YACX;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;;YACE,8BACC,6LAAC;gBAAI,WAAW,CAAC,oBAAoB,EAAE,aAAa,OAAO,GAAG,+BAA+B,0BAA0B;0BACpH,aAAa,OAAO;;;;;;0BAIzB,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAY,WAAU;0CAA+C;;;;;;0CAGpF,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,SAAS;gCACzB,UAAU;gCACV,WAAW,CAAC,gGAAgG,EAAE,OAAO,SAAS,GAAG,mBAAmB,mBAAmB;;;;;;4BAExK,OAAO,SAAS,kBAAI,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,SAAS;;;;;;;;;;;;kCAGjF,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA+C;;;;;;0CAGnF,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,QAAQ;gCACxB,UAAU;gCACV,WAAW,CAAC,gGAAgG,EAAE,OAAO,QAAQ,GAAG,mBAAmB,mBAAmB;;;;;;4BAEvK,OAAO,QAAQ,kBAAI,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,QAAQ;;;;;;;;;;;;kCAG/E,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA+C;;;;;;0CAGnF,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,QAAQ;gCACxB,UAAU;gCACV,WAAW,CAAC,gGAAgG,EAAE,OAAO,QAAQ,GAAG,mBAAmB,mBAAmB;;kDAEtK,6LAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,6LAAC;wCAAO,OAAM;kDAAe;;;;;;kDAC7B,6LAAC;wCAAO,OAAM;kDAAoB;;;;;;kDAClC,6LAAC;wCAAO,OAAM;kDAAgB;;;;;;;;;;;;4BAE/B,OAAO,QAAQ,kBAAI,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,QAAQ;;;;;;;;;;;;kCAG/E,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAa,WAAU;0CAA+C;;;;;;0CAGrF,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,UAAU;gCAC1B,UAAU;gCACV,WAAW,CAAC,gGAAgG,EAAE,OAAO,UAAU,GAAG,mBAAmB,mBAAmB;;;;;;4BAEzK,OAAO,UAAU,kBAAI,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,UAAU;;;;;;;;;;;;kCAGnF,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA+C;;;;;;0CAGhF,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU;gCACV,WAAW,CAAC,gGAAgG,EAAE,OAAO,KAAK,GAAG,mBAAmB,mBAAmB;;;;;;4BAEpK,OAAO,KAAK,kBAAI,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK;;;;;;;;;;;;kCAGzE,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA+C;;;;;;0CAGhF,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU;gCACV,WAAW,CAAC,gGAAgG,EAAE,OAAO,KAAK,GAAG,mBAAmB,mBAAmB;;;;;;4BAEpK,OAAO,KAAK,kBAAI,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK;;;;;;;;;;;;kCAGzE,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAmB,WAAU;0CAA+C;;;;;;0CAG3F,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,gBAAgB;gCAChC,UAAU;gCACV,aAAY;gCACZ,WAAW,CAAC,gGAAgG,EAAE,OAAO,gBAAgB,GAAG,mBAAmB,mBAAmB;;;;;;4BAE/K,OAAO,gBAAgB,kBAAI,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,gBAAgB;;;;;;;;;;;;kCAG/F,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAc,WAAU;0CAA+C;;;;;;0CAGtF,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,WAAW;gCAC3B,UAAU;gCACV,aAAY;gCACZ,MAAM;gCACN,WAAU;;;;;;;;;;;;kCAId,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,MAAK;oCACL,SAAS,SAAS,YAAY;oCAC9B,UAAU;oCACV,WAAU;;;;;;;;;;;0CAGd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,SAAQ;oCAAe,WAAU;8CAA4B;;;;;;;;;;;;;;;;;kCAMxE,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAU;sCAET,6BACC;;kDACE,6LAAC;wCAAI,WAAU;wCAA6C,OAAM;wCAA6B,MAAK;wCAAO,SAAQ;;0DACjH,6LAAC;gDAAO,WAAU;gDAAa,IAAG;gDAAK,IAAG;gDAAK,GAAE;gDAAK,QAAO;gDAAe,aAAY;;;;;;0DACxF,6LAAC;gDAAK,WAAU;gDAAa,MAAK;gDAAe,GAAE;;;;;;;;;;;;oCAC/C;;+CAIR;;;;;;;;;;;kCAKN,6LAAC;wBAAE,WAAU;;4BAAyC;0CAEpD,6LAAC;;;;;4BAAK;0CACe,6LAAC;gCAAE,MAAK;gCAA8B,WAAU;0CAAiC;;;;;;;;;;;;;;;;;;;;;;;;AAKhH;GAnTwB;KAAA"}}, {"offset": {"line": 1096, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}