{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/DirectRegistrationForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, FormEvent, ChangeEvent } from 'react';\n\ninterface FormValues {\n  childName: string;\n  childDob: string;\n  ageGroup: string;\n  parentName: string;\n  email: string;\n  phone: string;\n  emergencyContact: string;\n  medicalInfo: string;\n  photoConsent: boolean;\n}\n\nexport default function DirectRegistrationForm() {\n  const [formData, setFormData] = useState<FormValues>({\n    childName: '',\n    childDob: '',\n    ageGroup: '',\n    parentName: '',\n    email: '',\n    phone: '',\n    emergencyContact: '',\n    medicalInfo: '',\n    photoConsent: false,\n  });\n\n  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value, type } = e.target;\n    const checked = type === 'checkbox' ? (e.target as HTMLInputElement).checked : undefined;\n    \n    setFormData({\n      ...formData,\n      [name]: type === 'checkbox' ? checked : value\n    });\n  };\n\n  const handleSubmit = (e: FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n    alert('Form submitted!');\n    // Basic validation and submission logic would go here\n  };\n\n  return (\n    <div>\n      <form onSubmit={handleSubmit} className=\"space-y-5\">\n        <div>\n          <label htmlFor=\"childName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Child&apos;s Full Name*\n          </label>\n          <input\n            type=\"text\"\n            id=\"childName\"\n            name=\"childName\"\n            value={formData.childName}\n            onChange={handleInputChange}\n            className=\"w-full px-3 py-2 border rounded-lg border-gray-300\"\n            required\n          />\n        </div>\n        \n        <div>\n          <label htmlFor=\"childDob\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Date of Birth*\n          </label>\n          <input\n            type=\"date\"\n            id=\"childDob\"\n            name=\"childDob\"\n            value={formData.childDob}\n            onChange={handleInputChange}\n            className=\"w-full px-3 py-2 border rounded-lg border-gray-300\"\n            required\n          />\n        </div>\n        \n        <div>\n          <label htmlFor=\"ageGroup\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Age Group*\n          </label>\n          <select\n            id=\"ageGroup\"\n            name=\"ageGroup\"\n            value={formData.ageGroup}\n            onChange={handleInputChange}\n            className=\"w-full px-3 py-2 border rounded-lg border-gray-300\"\n            required\n          >\n            <option value=\"\">Select age group</option>\n            <option value=\"little-stars\">Little Stars (Ages 5-7)</option>\n            <option value=\"junior-developers\">Junior Developers (Ages 8-11)</option>\n            <option value=\"youth-talents\">Youth Talents (Ages 12-15)</option>\n          </select>\n        </div>\n        \n        <div>\n          <label htmlFor=\"parentName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Parent/Guardian Name*\n          </label>\n          <input\n            type=\"text\"\n            id=\"parentName\"\n            name=\"parentName\"\n            value={formData.parentName}\n            onChange={handleInputChange}\n            className=\"w-full px-3 py-2 border rounded-lg border-gray-300\"\n            required\n          />\n        </div>\n        \n        <div>\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Email Address*\n          </label>\n          <input\n            type=\"email\"\n            id=\"email\"\n            name=\"email\"\n            value={formData.email}\n            onChange={handleInputChange}\n            className=\"w-full px-3 py-2 border rounded-lg border-gray-300\"\n            required\n          />\n        </div>\n        \n        <div>\n          <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Phone Number*\n          </label>\n          <input\n            type=\"tel\"\n            id=\"phone\"\n            name=\"phone\"\n            value={formData.phone}\n            onChange={handleInputChange}\n            className=\"w-full px-3 py-2 border rounded-lg border-gray-300\"\n            required\n          />\n        </div>\n        \n        <div>\n          <label htmlFor=\"emergencyContact\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Emergency Contact*\n          </label>\n          <input\n            type=\"text\"\n            id=\"emergencyContact\"\n            name=\"emergencyContact\"\n            value={formData.emergencyContact}\n            onChange={handleInputChange}\n            placeholder=\"Name and phone number\"\n            className=\"w-full px-3 py-2 border rounded-lg border-gray-300\"\n            required\n          />\n        </div>\n        \n        <div>\n          <label htmlFor=\"medicalInfo\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Medical Information (optional)\n          </label>\n          <textarea\n            id=\"medicalInfo\"\n            name=\"medicalInfo\"\n            value={formData.medicalInfo}\n            onChange={handleInputChange}\n            placeholder=\"Please inform us of any medical conditions, allergies, or special needs\"\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg\"\n          />\n        </div>\n        \n        <div className=\"flex items-start\">\n          <div className=\"flex items-center h-5\">\n            <input\n              id=\"photoConsent\"\n              name=\"photoConsent\"\n              type=\"checkbox\"\n              checked={formData.photoConsent}\n              onChange={handleInputChange}\n              className=\"w-4 h-4 text-primary-600 border-gray-300 rounded\"\n            />\n          </div>\n          <div className=\"ml-3 text-sm\">\n            <label htmlFor=\"photoConsent\" className=\"font-medium text-gray-700\">\n              I consent to the use of photos/videos of my child for club promotional purposes\n            </label>\n          </div>\n        </div>\n        \n        <div className=\"pt-2\">\n          <button\n            type=\"submit\"\n            className=\"w-full bg-primary-600 hover:bg-primary-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors duration-300\"\n          >\n            Submit Registration\n          </button>\n        </div>\n        \n        <p className=\"text-sm text-gray-500 text-center mt-4\">\n          Places are limited and allocated on a first-come, first-served basis.\n          <br />\n          For questions, email <a href=\"mailto:<EMAIL>\" className=\"text-primary-600 font-semibold\"><EMAIL></a>\n        </p>\n      </form>\n    </div>\n  );\n} "], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAgBe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QACnD,WAAW;QACX,UAAU;QACV,UAAU;QACV,YAAY;QACZ,OAAO;QACP,OAAO;QACP,kBAAkB;QAClB,aAAa;QACb,cAAc;IAChB;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,MAAM,UAAU,SAAS,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAAG;QAE/E,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;QAC1C;IACF;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,MAAM;IACN,sDAAsD;IACxD;IAEA,qBACE,6LAAC;kBACC,cAAA,6LAAC;YAAK,UAAU;YAAc,WAAU;;8BACtC,6LAAC;;sCACC,6LAAC;4BAAM,SAAQ;4BAAY,WAAU;sCAA+C;;;;;;sCAGpF,6LAAC;4BACC,MAAK;4BACL,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,SAAS;4BACzB,UAAU;4BACV,WAAU;4BACV,QAAQ;;;;;;;;;;;;8BAIZ,6LAAC;;sCACC,6LAAC;4BAAM,SAAQ;4BAAW,WAAU;sCAA+C;;;;;;sCAGnF,6LAAC;4BACC,MAAK;4BACL,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,QAAQ;4BACxB,UAAU;4BACV,WAAU;4BACV,QAAQ;;;;;;;;;;;;8BAIZ,6LAAC;;sCACC,6LAAC;4BAAM,SAAQ;4BAAW,WAAU;sCAA+C;;;;;;sCAGnF,6LAAC;4BACC,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,QAAQ;4BACxB,UAAU;4BACV,WAAU;4BACV,QAAQ;;8CAER,6LAAC;oCAAO,OAAM;8CAAG;;;;;;8CACjB,6LAAC;oCAAO,OAAM;8CAAe;;;;;;8CAC7B,6LAAC;oCAAO,OAAM;8CAAoB;;;;;;8CAClC,6LAAC;oCAAO,OAAM;8CAAgB;;;;;;;;;;;;;;;;;;8BAIlC,6LAAC;;sCACC,6LAAC;4BAAM,SAAQ;4BAAa,WAAU;sCAA+C;;;;;;sCAGrF,6LAAC;4BACC,MAAK;4BACL,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,UAAU;4BAC1B,UAAU;4BACV,WAAU;4BACV,QAAQ;;;;;;;;;;;;8BAIZ,6LAAC;;sCACC,6LAAC;4BAAM,SAAQ;4BAAQ,WAAU;sCAA+C;;;;;;sCAGhF,6LAAC;4BACC,MAAK;4BACL,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,KAAK;4BACrB,UAAU;4BACV,WAAU;4BACV,QAAQ;;;;;;;;;;;;8BAIZ,6LAAC;;sCACC,6LAAC;4BAAM,SAAQ;4BAAQ,WAAU;sCAA+C;;;;;;sCAGhF,6LAAC;4BACC,MAAK;4BACL,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,KAAK;4BACrB,UAAU;4BACV,WAAU;4BACV,QAAQ;;;;;;;;;;;;8BAIZ,6LAAC;;sCACC,6LAAC;4BAAM,SAAQ;4BAAmB,WAAU;sCAA+C;;;;;;sCAG3F,6LAAC;4BACC,MAAK;4BACL,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,gBAAgB;4BAChC,UAAU;4BACV,aAAY;4BACZ,WAAU;4BACV,QAAQ;;;;;;;;;;;;8BAIZ,6LAAC;;sCACC,6LAAC;4BAAM,SAAQ;4BAAc,WAAU;sCAA+C;;;;;;sCAGtF,6LAAC;4BACC,IAAG;4BACH,MAAK;4BACL,OAAO,SAAS,WAAW;4BAC3B,UAAU;4BACV,aAAY;4BACZ,MAAM;4BACN,WAAU;;;;;;;;;;;;8BAId,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,MAAK;gCACL,SAAS,SAAS,YAAY;gCAC9B,UAAU;gCACV,WAAU;;;;;;;;;;;sCAGd,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,SAAQ;gCAAe,WAAU;0CAA4B;;;;;;;;;;;;;;;;;8BAMxE,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBACC,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;8BAKH,6LAAC;oBAAE,WAAU;;wBAAyC;sCAEpD,6LAAC;;;;;wBAAK;sCACe,6LAAC;4BAAE,MAAK;4BAA8B,WAAU;sCAAiC;;;;;;;;;;;;;;;;;;;;;;;AAKhH;GAhMwB;KAAA"}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}