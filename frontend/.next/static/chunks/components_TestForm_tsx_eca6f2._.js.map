{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/TestForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\nexport default function TestForm() {\n  const [testValue, setTestValue] = useState('');\n\n  return (\n    <div className=\"p-4 border border-red-500 bg-red-50\">\n      <h3 className=\"text-lg font-bold mb-4 text-red-800\">TEST FORM - Debug</h3>\n      <div className=\"space-y-4\">\n        <div>\n          <label htmlFor=\"test-input\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Test Input (should be editable):\n          </label>\n          <input\n            type=\"text\"\n            id=\"test-input\"\n            value={testValue}\n            onChange={(e) => {\n              console.log('Test input changed:', e.target.value);\n              setTestValue(e.target.value);\n            }}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500\"\n            placeholder=\"Type here to test...\"\n          />\n          <p className=\"text-sm text-gray-600 mt-1\">Current value: \"{testValue}\"</p>\n        </div>\n        \n        <div>\n          <button\n            type=\"button\"\n            onClick={() => {\n              console.log('Button clicked, current value:', testValue);\n              alert(`Current value: ${testValue}`);\n            }}\n            className=\"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700\"\n          >\n            Test Button\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAIe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAAsC;;;;;;0BACpD,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAa,WAAU;0CAA+C;;;;;;0CAGrF,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,OAAO;gCACP,UAAU,CAAC;oCACT,QAAQ,GAAG,CAAC,uBAAuB,EAAE,MAAM,CAAC,KAAK;oCACjD,aAAa,EAAE,MAAM,CAAC,KAAK;gCAC7B;gCACA,WAAU;gCACV,aAAY;;;;;;0CAEd,6LAAC;gCAAE,WAAU;;oCAA6B;oCAAiB;oCAAU;;;;;;;;;;;;;kCAGvE,6LAAC;kCACC,cAAA,6LAAC;4BACC,MAAK;4BACL,SAAS;gCACP,QAAQ,GAAG,CAAC,kCAAkC;gCAC9C,MAAM,CAAC,eAAe,EAAE,WAAW;4BACrC;4BACA,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;;AAOX;GAxCwB;KAAA"}}, {"offset": {"line": 116, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}