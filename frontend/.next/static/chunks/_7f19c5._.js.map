{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Header.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport Image from 'next/image'\nimport { useState, useEffect } from 'react'\n\nexport default function Header() {\n  const [isMenuOpen, setIsMenuOpen] = useState(false)\n  const [isScrolled, setIsScrolled] = useState(false)\n\n  // Handle scroll effect for sticky header\n  useEffect(() => {\n    const handleScroll = () => {\n      if (window.scrollY > 10) {\n        setIsScrolled(true)\n      } else {\n        setIsScrolled(false)\n      }\n    }\n\n    window.addEventListener('scroll', handleScroll)\n    return () => window.removeEventListener('scroll', handleScroll)\n  }, [])\n\n  const toggleMenu = () => {\n    setIsMenuOpen(!isMenuOpen)\n  }\n\n  return (\n    <header\n      className={`bg-white sticky top-0 z-50 transition-all duration-200 ${\n        isScrolled ? 'shadow-lg py-2' : 'shadow-md py-3'\n      }`}\n    >\n      <div className=\"container\">\n        <div className=\"flex justify-between items-center\">\n          <Link href=\"/\" className=\"flex items-center space-x-3\">\n            <div className={`relative transition-all duration-200 ${isScrolled ? 'w-10 h-10' : 'w-12 h-12'}`}>\n              <Image\n                src=\"/logo.png\"\n                alt=\"Northern Nepalese United FC Logo\"\n                fill\n                className=\"object-contain\"\n                priority\n              />\n            </div>\n            <div>\n              <span className=\"text-xl font-bold text-primary-800 hidden sm:inline\">Northern Nepalese United FC</span>\n              <span className=\"text-xl font-bold text-primary-800 sm:hidden\">NNUFC</span>\n            </div>\n          </Link>\n\n          {/* Mobile menu button */}\n          <button\n            className=\"md:hidden p-2 text-primary-800 focus:outline-none focus:ring-2 focus:ring-primary-400 rounded-lg\"\n            onClick={toggleMenu}\n            aria-label=\"Toggle menu\"\n            aria-expanded={isMenuOpen}\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              fill=\"none\"\n              viewBox=\"0 0 24 24\"\n              stroke=\"currentColor\"\n              className=\"h-6 w-6\"\n            >\n              {isMenuOpen ? (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M6 18L18 6M6 6l12 12\"\n                />\n              ) : (\n                <path\n                  strokeLinecap=\"round\"\n                  strokeLinejoin=\"round\"\n                  strokeWidth={2}\n                  d=\"M4 6h16M4 12h16M4 18h16\"\n                />\n              )}\n            </svg>\n          </button>\n\n          {/* Desktop navigation */}\n          <nav className=\"hidden md:flex space-x-1 lg:space-x-6\">\n            <NavLink href=\"/\">Home</NavLink>\n            <NavLink href=\"/about\">About</NavLink>\n            <NavLink href=\"/team\">Team</NavLink>\n            <NavLink href=\"/junior-academy\">Junior Academy</NavLink>\n            <NavLink href=\"/news\">News</NavLink>\n            <NavLink href=\"/events\">Events</NavLink>\n            <NavLink href=\"/sponsors\">Sponsors</NavLink>\n            <NavLink href=\"/gallery\">Gallery</NavLink>\n            <NavLink href=\"/contact\">Contact</NavLink>\n          </nav>\n        </div>\n\n        {/* Mobile navigation - slide down animation */}\n        <div\n          className={`md:hidden overflow-hidden transition-all duration-300 ease-in-out ${\n            isMenuOpen ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'\n          }`}\n        >\n          <nav className=\"mt-4 space-y-2 border-t border-neutral-200 pt-3\">\n            <MobileNavLink href=\"/\" onClick={() => setIsMenuOpen(false)}>Home</MobileNavLink>\n            <MobileNavLink href=\"/about\" onClick={() => setIsMenuOpen(false)}>About</MobileNavLink>\n            <MobileNavLink href=\"/team\" onClick={() => setIsMenuOpen(false)}>Team</MobileNavLink>\n            <MobileNavLink href=\"/junior-academy\" onClick={() => setIsMenuOpen(false)}>Junior Academy</MobileNavLink>\n            <MobileNavLink href=\"/news\" onClick={() => setIsMenuOpen(false)}>News</MobileNavLink>\n            <MobileNavLink href=\"/events\" onClick={() => setIsMenuOpen(false)}>Events</MobileNavLink>\n            <MobileNavLink href=\"/sponsors\" onClick={() => setIsMenuOpen(false)}>Sponsors</MobileNavLink>\n            <MobileNavLink href=\"/gallery\" onClick={() => setIsMenuOpen(false)}>Gallery</MobileNavLink>\n            <MobileNavLink href=\"/contact\" onClick={() => setIsMenuOpen(false)}>Contact</MobileNavLink>\n          </nav>\n        </div>\n      </div>\n    </header>\n  )\n}\n\n// Desktop Navigation Link Component\nfunction NavLink({ href, children }: { href: string; children: React.ReactNode }) {\n  return (\n    <Link\n      href={href}\n      className=\"px-3 py-2 rounded-lg font-medium text-primary-700 transition-colors hover:bg-primary-50 hover:text-primary-900\"\n    >\n      {children}\n    </Link>\n  )\n}\n\n// Mobile Navigation Link Component\nfunction MobileNavLink({ href, onClick, children }: {\n  href: string;\n  onClick: () => void;\n  children: React.ReactNode\n}) {\n  return (\n    <Link\n      href={href}\n      className=\"block py-2 px-1 text-primary-700 hover:bg-primary-50 hover:text-primary-900 rounded-lg font-medium transition-colors\"\n      onClick={onClick}\n    >\n      {children}\n    </Link>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,IAAI,OAAO,OAAO,GAAG,IAAI;wBACvB,cAAc;oBAChB,OAAO;wBACL,cAAc;oBAChB;gBACF;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,MAAM,aAAa;QACjB,cAAc,CAAC;IACjB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAC,uDAAuD,EACjE,aAAa,mBAAmB,kBAChC;kBAEF,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,+JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,6LAAC;oCAAI,WAAW,CAAC,qCAAqC,EAAE,aAAa,cAAc,aAAa;8CAC9F,cAAA,6LAAC,gIAAA,CAAA,UAAK;wCACJ,KAAI;wCACJ,KAAI;wCACJ,IAAI;wCACJ,WAAU;wCACV,QAAQ;;;;;;;;;;;8CAGZ,6LAAC;;sDACC,6LAAC;4CAAK,WAAU;sDAAsD;;;;;;sDACtE,6LAAC;4CAAK,WAAU;sDAA+C;;;;;;;;;;;;;;;;;;sCAKnE,6LAAC;4BACC,WAAU;4BACV,SAAS;4BACT,cAAW;4BACX,iBAAe;sCAEf,cAAA,6LAAC;gCACC,OAAM;gCACN,MAAK;gCACL,SAAQ;gCACR,QAAO;gCACP,WAAU;0CAET,2BACC,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;yDAGJ,6LAAC;oCACC,eAAc;oCACd,gBAAe;oCACf,aAAa;oCACb,GAAE;;;;;;;;;;;;;;;;sCAOV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAQ,MAAK;8CAAI;;;;;;8CAClB,6LAAC;oCAAQ,MAAK;8CAAS;;;;;;8CACvB,6LAAC;oCAAQ,MAAK;8CAAQ;;;;;;8CACtB,6LAAC;oCAAQ,MAAK;8CAAkB;;;;;;8CAChC,6LAAC;oCAAQ,MAAK;8CAAQ;;;;;;8CACtB,6LAAC;oCAAQ,MAAK;8CAAU;;;;;;8CACxB,6LAAC;oCAAQ,MAAK;8CAAY;;;;;;8CAC1B,6LAAC;oCAAQ,MAAK;8CAAW;;;;;;8CACzB,6LAAC;oCAAQ,MAAK;8CAAW;;;;;;;;;;;;;;;;;;8BAK7B,6LAAC;oBACC,WAAW,CAAC,kEAAkE,EAC5E,aAAa,yBAAyB,qBACtC;8BAEF,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAc,MAAK;gCAAI,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CAC7D,6LAAC;gCAAc,MAAK;gCAAS,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CAClE,6LAAC;gCAAc,MAAK;gCAAQ,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACjE,6LAAC;gCAAc,MAAK;gCAAkB,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CAC3E,6LAAC;gCAAc,MAAK;gCAAQ,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACjE,6LAAC;gCAAc,MAAK;gCAAU,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACnE,6LAAC;gCAAc,MAAK;gCAAY,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACrE,6LAAC;gCAAc,MAAK;gCAAW,SAAS,IAAM,cAAc;0CAAQ;;;;;;0CACpE,6LAAC;gCAAc,MAAK;gCAAW,SAAS,IAAM,cAAc;0CAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMhF;GAjHwB;KAAA;AAmHxB,oCAAoC;AACpC,SAAS,QAAQ,EAAE,IAAI,EAAE,QAAQ,EAA+C;IAC9E,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAU;kBAET;;;;;;AAGP;MATS;AAWT,mCAAmC;AACnC,SAAS,cAAc,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAI/C;IACC,qBACE,6LAAC,+JAAA,CAAA,UAAI;QACH,MAAM;QACN,WAAU;QACV,SAAS;kBAER;;;;;;AAGP;MAdS"}}, {"offset": {"line": 377, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 383, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Footer.tsx"], "sourcesContent": ["import Link from 'next/link'\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear()\n\n  return (\n    <footer className=\"bg-gradient-to-b from-neutral-800 to-neutral-900 text-white\">\n      <div className=\"container py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div className=\"md:col-span-2\">\n            <h3 className=\"text-xl font-bold mb-4 text-white\">Northern Nepalese United FC</h3>\n            <p className=\"mb-4 text-neutral-300\">\n              Brisbane-based Nepalese football club est. 2023, bringing together the community through the beautiful game.\n            </p>\n            <div className=\"flex space-x-4 mt-6\">\n              <a\n                href=\"https://facebook.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                aria-label=\"Facebook\"\n                className=\"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"currentColor\"\n                >\n                  <path d=\"M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z\" />\n                </svg>\n              </a>\n              <a\n                href=\"https://twitter.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                aria-label=\"Twitter\"\n                className=\"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"currentColor\"\n                >\n                  <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\" />\n                </svg>\n              </a>\n              <a\n                href=\"https://instagram.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                aria-label=\"Instagram\"\n                className=\"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"currentColor\"\n                >\n                  <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          <div>\n            <h3 className=\"text-xl font-bold mb-4 text-white\">Club Links</h3>\n            <ul className=\"space-y-2 text-neutral-300\">\n              <li>\n                <Link href=\"/about\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  About Us\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/team\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  Our Team\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/news\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  News\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/events\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  Events & Fixtures\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/gallery\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  Gallery\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"text-xl font-bold mb-4 text-white\">Contact</h3>\n            <address className=\"not-italic text-neutral-300\">\n              <p className=\"mb-2 flex items-start\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 text-neutral-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                </svg>\n                John Oxley Reserve<br />Murrumba Downs<br />Brisbane, QLD\n              </p>\n              <p className=\"mb-2 flex items-start\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 text-neutral-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                </svg>\n                <a href=\"tel:+61424770570\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  0424 770 570\n                </a>\n              </p>\n              <p className=\"flex items-start\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 text-neutral-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                </svg>\n                <a\n                  href=\"mailto:<EMAIL>\"\n                  className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\"\n                >\n                  <EMAIL>\n                </a>\n              </p>\n            </address>\n          </div>\n        </div>\n\n        <div className=\"border-t border-neutral-700 mt-10 pt-8 text-center text-neutral-400\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p>&copy; {currentYear} Northern Nepalese United - NNUFC. All rights reserved.</p>\n            <ul className=\"flex space-x-4 mt-4 md:mt-0\">\n              <li>\n                <Link href=\"/sponsors\" className=\"text-neutral-400 hover:text-white hover:underline transition-colors no-underline\">\n                  Sponsors\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-neutral-400 hover:text-white hover:underline transition-colors no-underline\">\n                  Contact\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,6LAAC;QAAO,WAAU;kBAChB,cAAA,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,6LAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,cAAW;4CACX,WAAU;sDAEV,cAAA,6LAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;0DAEL,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,cAAW;4CACX,WAAU;sDAEV,cAAA,6LAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;0DAEL,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,6LAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,cAAW;4CACX,WAAU;sDAEV,cAAA,6LAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;0DAEL,cAAA,6LAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMhB,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmF;;;;;;;;;;;sDAInH,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmF;;;;;;;;;;;sDAIlH,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmF;;;;;;;;;;;sDAIlH,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAmF;;;;;;;;;;;sDAIpH,6LAAC;sDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmF;;;;;;;;;;;;;;;;;;;;;;;sCAOzH,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,6LAAC;oCAAQ,WAAU;;sDACjB,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAuC,MAAK;oDAAO,SAAQ;oDAAY,QAAO;;sEAC9H,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;sEACrE,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;gDACjE;8DACY,6LAAC;;;;;gDAAK;8DAAc,6LAAC;;;;;gDAAK;;;;;;;sDAE9C,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAuC,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAC9H,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDAAE,MAAK;oDAAmB,WAAU;8DAAmF;;;;;;;;;;;;sDAI1H,6LAAC;4CAAE,WAAU;;8DACX,6LAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAuC,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAC9H,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,6LAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQT,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;oCAAE;oCAAQ;oCAAY;;;;;;;0CACvB,6LAAC;gCAAG,WAAU;;kDACZ,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAY,WAAU;sDAAmF;;;;;;;;;;;kDAItH,6LAAC;kDACC,cAAA,6LAAC,+JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAmF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnI;KAvJwB"}}, {"offset": {"line": 869, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 875, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/DirectRegistrationForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, FormEvent, ChangeEvent } from 'react';\n\ninterface FormValues {\n  childName: string;\n  childDob: string;\n  ageGroup: string;\n  parentName: string;\n  email: string;\n  phone: string;\n  emergencyContact: string;\n  medicalInfo: string;\n  photoConsent: boolean;\n}\n\ninterface SubmitStatus {\n  success?: boolean;\n  message?: string;\n}\n\nexport default function DirectRegistrationForm() {\n  const [formData, setFormData] = useState<FormValues>({\n    childName: '',\n    childDob: '',\n    ageGroup: '',\n    parentName: '',\n    email: '',\n    phone: '',\n    emergencyContact: '',\n    medicalInfo: '',\n    photoConsent: false,\n  });\n\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [submitStatus, setSubmitStatus] = useState<SubmitStatus | null>(null);\n  const [errors, setErrors] = useState<Partial<FormValues>>({});\n\n  const handleInputChange = (e: ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    const { name, value, type } = e.target;\n    const checked = type === 'checkbox' ? (e.target as HTMLInputElement).checked : undefined;\n\n    console.log('Input change:', { name, value, type, checked }); // Debug log\n\n    setFormData(prev => ({\n      ...prev,\n      [name]: type === 'checkbox' ? checked : value\n    }));\n\n    // Clear any existing error for this field\n    if (errors[name as keyof FormValues]) {\n      setErrors(prev => ({\n        ...prev,\n        [name]: undefined\n      }));\n    }\n  };\n\n  const handleInputFocus = (e: React.FocusEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    console.log('Input focused:', e.target.name);\n  };\n\n  const handleInputClick = (e: React.MouseEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {\n    console.log('Input clicked:', e.currentTarget.name);\n    e.stopPropagation();\n  };\n\n  const validateForm = (): boolean => {\n    const newErrors: Partial<FormValues> = {};\n\n    if (!formData.childName.trim()) newErrors.childName = 'Child name is required';\n    if (!formData.childDob) newErrors.childDob = 'Date of birth is required';\n    if (!formData.ageGroup) newErrors.ageGroup = 'Age group is required';\n    if (!formData.parentName.trim()) newErrors.parentName = 'Parent/Guardian name is required';\n    if (!formData.email.trim()) newErrors.email = 'Email is required';\n    if (!formData.phone.trim()) newErrors.phone = 'Phone number is required';\n    if (!formData.emergencyContact.trim()) newErrors.emergencyContact = 'Emergency contact is required';\n\n    setErrors(newErrors);\n    return Object.keys(newErrors).length === 0;\n  };\n\n  const handleSubmit = async (e: FormEvent<HTMLFormElement>) => {\n    e.preventDefault();\n\n    if (!validateForm()) {\n      return;\n    }\n\n    setIsSubmitting(true);\n    setSubmitStatus(null);\n\n    try {\n      const response = await fetch('/api/junior-registration', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify(formData),\n      });\n\n      const data = await response.json();\n\n      if (!response.ok) {\n        throw new Error(data.error || 'Failed to submit registration');\n      }\n\n      setSubmitStatus({\n        success: true,\n        message: data.message || 'Registration submitted successfully! We will contact you soon with further details.'\n      });\n\n      // Reset form after successful submission\n      setFormData({\n        childName: '',\n        childDob: '',\n        ageGroup: '',\n        parentName: '',\n        email: '',\n        phone: '',\n        emergencyContact: '',\n        medicalInfo: '',\n        photoConsent: false,\n      });\n\n    } catch (error) {\n      console.error('Registration error:', error);\n      setSubmitStatus({\n        success: false,\n        message: error instanceof Error ? error.message : 'Failed to submit registration. Please try again.'\n      });\n    } finally {\n      setIsSubmitting(false);\n    }\n  };\n\n  return (\n    <div>\n      {/* Success/Error Messages */}\n      {submitStatus && (\n        <div className={`mb-6 p-4 rounded-lg ${\n          submitStatus.success\n            ? 'bg-green-50 border border-green-200 text-green-800'\n            : 'bg-red-50 border border-red-200 text-red-800'\n        }`}>\n          <div className=\"flex\">\n            <div className=\"flex-shrink-0\">\n              {submitStatus.success ? (\n                <svg className=\"h-5 w-5 text-green-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                </svg>\n              ) : (\n                <svg className=\"h-5 w-5 text-red-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n                </svg>\n              )}\n            </div>\n            <div className=\"ml-3\">\n              <p className=\"text-sm font-medium\">{submitStatus.message}</p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      <form onSubmit={handleSubmit} className=\"space-y-5\">\n        <div>\n          <label htmlFor=\"childName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Child&apos;s Full Name*\n          </label>\n          <input\n            type=\"text\"\n            id=\"childName\"\n            name=\"childName\"\n            value={formData.childName}\n            onChange={handleInputChange}\n            onFocus={handleInputFocus}\n            onClick={handleInputClick}\n            className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 hover:border-gray-400 ${\n              errors.childName ? 'border-red-500 bg-red-50' : 'border-gray-300 bg-white'\n            }`}\n            placeholder=\"Enter child's full name\"\n            style={{ pointerEvents: 'auto', userSelect: 'text' }}\n            required\n          />\n          {errors.childName && <p className=\"mt-1 text-sm text-red-600\">{errors.childName}</p>}\n        </div>\n\n        <div>\n          <label htmlFor=\"childDob\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Date of Birth*\n          </label>\n          <input\n            type=\"date\"\n            id=\"childDob\"\n            name=\"childDob\"\n            value={formData.childDob}\n            onChange={handleInputChange}\n            className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 hover:border-gray-400 ${\n              errors.childDob ? 'border-red-500 bg-red-50' : 'border-gray-300 bg-white'\n            }`}\n            required\n          />\n          {errors.childDob && <p className=\"mt-1 text-sm text-red-600\">{errors.childDob}</p>}\n        </div>\n\n        <div>\n          <label htmlFor=\"ageGroup\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Age Group*\n          </label>\n          <select\n            id=\"ageGroup\"\n            name=\"ageGroup\"\n            value={formData.ageGroup}\n            onChange={handleInputChange}\n            className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 hover:border-gray-400 ${\n              errors.ageGroup ? 'border-red-500 bg-red-50' : 'border-gray-300 bg-white'\n            }`}\n            required\n          >\n            <option value=\"\">Select age group</option>\n            <option value=\"little-stars\">Little Stars (Ages 5-7)</option>\n            <option value=\"junior-developers\">Junior Developers (Ages 8-11)</option>\n            <option value=\"youth-talents\">Youth Talents (Ages 12-15)</option>\n          </select>\n          {errors.ageGroup && <p className=\"mt-1 text-sm text-red-600\">{errors.ageGroup}</p>}\n        </div>\n\n        <div>\n          <label htmlFor=\"parentName\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Parent/Guardian Name*\n          </label>\n          <input\n            type=\"text\"\n            id=\"parentName\"\n            name=\"parentName\"\n            value={formData.parentName}\n            onChange={handleInputChange}\n            className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 hover:border-gray-400 ${\n              errors.parentName ? 'border-red-500 bg-red-50' : 'border-gray-300 bg-white'\n            }`}\n            placeholder=\"Enter parent/guardian name\"\n            required\n          />\n          {errors.parentName && <p className=\"mt-1 text-sm text-red-600\">{errors.parentName}</p>}\n        </div>\n\n        <div>\n          <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Email Address*\n          </label>\n          <input\n            type=\"email\"\n            id=\"email\"\n            name=\"email\"\n            value={formData.email}\n            onChange={handleInputChange}\n            className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 hover:border-gray-400 ${\n              errors.email ? 'border-red-500 bg-red-50' : 'border-gray-300 bg-white'\n            }`}\n            placeholder=\"Enter email address\"\n            required\n          />\n          {errors.email && <p className=\"mt-1 text-sm text-red-600\">{errors.email}</p>}\n        </div>\n\n        <div>\n          <label htmlFor=\"phone\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Phone Number*\n          </label>\n          <input\n            type=\"tel\"\n            id=\"phone\"\n            name=\"phone\"\n            value={formData.phone}\n            onChange={handleInputChange}\n            className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 hover:border-gray-400 ${\n              errors.phone ? 'border-red-500 bg-red-50' : 'border-gray-300 bg-white'\n            }`}\n            placeholder=\"Enter phone number\"\n            required\n          />\n          {errors.phone && <p className=\"mt-1 text-sm text-red-600\">{errors.phone}</p>}\n        </div>\n\n        <div>\n          <label htmlFor=\"emergencyContact\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Emergency Contact*\n          </label>\n          <input\n            type=\"text\"\n            id=\"emergencyContact\"\n            name=\"emergencyContact\"\n            value={formData.emergencyContact}\n            onChange={handleInputChange}\n            className={`w-full px-3 py-2 border rounded-lg transition-colors duration-200 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 hover:border-gray-400 ${\n              errors.emergencyContact ? 'border-red-500 bg-red-50' : 'border-gray-300 bg-white'\n            }`}\n            placeholder=\"Name and phone number\"\n            required\n          />\n          {errors.emergencyContact && <p className=\"mt-1 text-sm text-red-600\">{errors.emergencyContact}</p>}\n        </div>\n\n        <div>\n          <label htmlFor=\"medicalInfo\" className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Medical Information (optional)\n          </label>\n          <textarea\n            id=\"medicalInfo\"\n            name=\"medicalInfo\"\n            value={formData.medicalInfo}\n            onChange={handleInputChange}\n            placeholder=\"Please inform us of any medical conditions, allergies, or special needs\"\n            rows={3}\n            className=\"w-full px-3 py-2 border border-gray-300 rounded-lg transition-colors duration-200 focus:ring-2 focus:ring-primary-500 focus:border-primary-500 hover:border-gray-400 bg-white\"\n          />\n        </div>\n\n        <div className=\"flex items-start\">\n          <div className=\"flex items-center h-5\">\n            <input\n              id=\"photoConsent\"\n              name=\"photoConsent\"\n              type=\"checkbox\"\n              checked={formData.photoConsent}\n              onChange={handleInputChange}\n              className=\"w-4 h-4 text-primary-600 border-gray-300 rounded focus:ring-2 focus:ring-primary-500\"\n            />\n          </div>\n          <div className=\"ml-3 text-sm\">\n            <label htmlFor=\"photoConsent\" className=\"font-medium text-gray-700 cursor-pointer\">\n              I consent to the use of photos/videos of my child for club promotional purposes\n            </label>\n          </div>\n        </div>\n\n        <div className=\"pt-2\">\n          <button\n            type=\"submit\"\n            disabled={isSubmitting}\n            className={`w-full font-semibold py-3 px-6 rounded-lg transition-all duration-300 ${\n              isSubmitting\n                ? 'bg-gray-400 cursor-not-allowed text-white'\n                : 'bg-primary-600 hover:bg-primary-700 text-white hover:shadow-lg transform hover:-translate-y-0.5'\n            }`}\n          >\n            {isSubmitting ? (\n              <div className=\"flex items-center justify-center\">\n                <svg className=\"animate-spin -ml-1 mr-3 h-5 w-5 text-white\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                  <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                  <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                </svg>\n                Submitting...\n              </div>\n            ) : (\n              'Submit Registration'\n            )}\n          </button>\n        </div>\n\n        <p className=\"text-sm text-gray-500 text-center mt-4\">\n          Places are limited and allocated on a first-come, first-served basis.\n          <br />\n          For questions, email <a href=\"mailto:<EMAIL>\" className=\"text-primary-600 font-semibold hover:text-primary-700 transition-colors\"><EMAIL></a>\n        </p>\n      </form>\n    </div>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAqBe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAc;QACnD,WAAW;QACX,UAAU;QACV,UAAU;QACV,YAAY;QACZ,OAAO;QACP,OAAO;QACP,kBAAkB;QAClB,aAAa;QACb,cAAc;IAChB;IAEA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,CAAC;IAE3D,MAAM,oBAAoB,CAAC;QACzB,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,IAAI,EAAE,GAAG,EAAE,MAAM;QACtC,MAAM,UAAU,SAAS,aAAa,AAAC,EAAE,MAAM,CAAsB,OAAO,GAAG;QAE/E,QAAQ,GAAG,CAAC,iBAAiB;YAAE;YAAM;YAAO;YAAM;QAAQ,IAAI,YAAY;QAE1E,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,KAAK,EAAE,SAAS,aAAa,UAAU;YAC1C,CAAC;QAED,0CAA0C;QAC1C,IAAI,MAAM,CAAC,KAAyB,EAAE;YACpC,UAAU,CAAA,OAAQ,CAAC;oBACjB,GAAG,IAAI;oBACP,CAAC,KAAK,EAAE;gBACV,CAAC;QACH;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ,GAAG,CAAC,kBAAkB,EAAE,MAAM,CAAC,IAAI;IAC7C;IAEA,MAAM,mBAAmB,CAAC;QACxB,QAAQ,GAAG,CAAC,kBAAkB,EAAE,aAAa,CAAC,IAAI;QAClD,EAAE,eAAe;IACnB;IAEA,MAAM,eAAe;QACnB,MAAM,YAAiC,CAAC;QAExC,IAAI,CAAC,SAAS,SAAS,CAAC,IAAI,IAAI,UAAU,SAAS,GAAG;QACtD,IAAI,CAAC,SAAS,QAAQ,EAAE,UAAU,QAAQ,GAAG;QAC7C,IAAI,CAAC,SAAS,QAAQ,EAAE,UAAU,QAAQ,GAAG;QAC7C,IAAI,CAAC,SAAS,UAAU,CAAC,IAAI,IAAI,UAAU,UAAU,GAAG;QACxD,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI,UAAU,KAAK,GAAG;QAC9C,IAAI,CAAC,SAAS,KAAK,CAAC,IAAI,IAAI,UAAU,KAAK,GAAG;QAC9C,IAAI,CAAC,SAAS,gBAAgB,CAAC,IAAI,IAAI,UAAU,gBAAgB,GAAG;QAEpE,UAAU;QACV,OAAO,OAAO,IAAI,CAAC,WAAW,MAAM,KAAK;IAC3C;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,gBAAgB;YACnB;QACF;QAEA,gBAAgB;QAChB,gBAAgB;QAEhB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,4BAA4B;gBACvD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,gBAAgB;gBACd,SAAS;gBACT,SAAS,KAAK,OAAO,IAAI;YAC3B;YAEA,yCAAyC;YACzC,YAAY;gBACV,WAAW;gBACX,UAAU;gBACV,UAAU;gBACV,YAAY;gBACZ,OAAO;gBACP,OAAO;gBACP,kBAAkB;gBAClB,aAAa;gBACb,cAAc;YAChB;QAEF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,gBAAgB;gBACd,SAAS;gBACT,SAAS,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACpD;QACF,SAAU;YACR,gBAAgB;QAClB;IACF;IAEA,qBACE,6LAAC;;YAEE,8BACC,6LAAC;gBAAI,WAAW,CAAC,oBAAoB,EACnC,aAAa,OAAO,GAChB,uDACA,gDACJ;0BACA,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,aAAa,OAAO,iBACnB,6LAAC;gCAAI,WAAU;gCAAyB,SAAQ;gCAAY,MAAK;0CAC/D,cAAA,6LAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAwI,UAAS;;;;;;;;;;qDAG9K,6LAAC;gCAAI,WAAU;gCAAuB,SAAQ;gCAAY,MAAK;0CAC7D,cAAA,6LAAC;oCAAK,UAAS;oCAAU,GAAE;oCAA0N,UAAS;;;;;;;;;;;;;;;;sCAIpQ,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAuB,aAAa,OAAO;;;;;;;;;;;;;;;;;;;;;;0BAMhE,6LAAC;gBAAK,UAAU;gBAAc,WAAU;;kCACtC,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAY,WAAU;0CAA+C;;;;;;0CAGpF,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,SAAS;gCACzB,UAAU;gCACV,SAAS;gCACT,SAAS;gCACT,WAAW,CAAC,qJAAqJ,EAC/J,OAAO,SAAS,GAAG,6BAA6B,4BAChD;gCACF,aAAY;gCACZ,OAAO;oCAAE,eAAe;oCAAQ,YAAY;gCAAO;gCACnD,QAAQ;;;;;;4BAET,OAAO,SAAS,kBAAI,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,SAAS;;;;;;;;;;;;kCAGjF,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA+C;;;;;;0CAGnF,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,QAAQ;gCACxB,UAAU;gCACV,WAAW,CAAC,qJAAqJ,EAC/J,OAAO,QAAQ,GAAG,6BAA6B,4BAC/C;gCACF,QAAQ;;;;;;4BAET,OAAO,QAAQ,kBAAI,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,QAAQ;;;;;;;;;;;;kCAG/E,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAW,WAAU;0CAA+C;;;;;;0CAGnF,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,QAAQ;gCACxB,UAAU;gCACV,WAAW,CAAC,qJAAqJ,EAC/J,OAAO,QAAQ,GAAG,6BAA6B,4BAC/C;gCACF,QAAQ;;kDAER,6LAAC;wCAAO,OAAM;kDAAG;;;;;;kDACjB,6LAAC;wCAAO,OAAM;kDAAe;;;;;;kDAC7B,6LAAC;wCAAO,OAAM;kDAAoB;;;;;;kDAClC,6LAAC;wCAAO,OAAM;kDAAgB;;;;;;;;;;;;4BAE/B,OAAO,QAAQ,kBAAI,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,QAAQ;;;;;;;;;;;;kCAG/E,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAa,WAAU;0CAA+C;;;;;;0CAGrF,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,UAAU;gCAC1B,UAAU;gCACV,WAAW,CAAC,qJAAqJ,EAC/J,OAAO,UAAU,GAAG,6BAA6B,4BACjD;gCACF,aAAY;gCACZ,QAAQ;;;;;;4BAET,OAAO,UAAU,kBAAI,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,UAAU;;;;;;;;;;;;kCAGnF,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA+C;;;;;;0CAGhF,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU;gCACV,WAAW,CAAC,qJAAqJ,EAC/J,OAAO,KAAK,GAAG,6BAA6B,4BAC5C;gCACF,aAAY;gCACZ,QAAQ;;;;;;4BAET,OAAO,KAAK,kBAAI,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK;;;;;;;;;;;;kCAGzE,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAQ,WAAU;0CAA+C;;;;;;0CAGhF,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,KAAK;gCACrB,UAAU;gCACV,WAAW,CAAC,qJAAqJ,EAC/J,OAAO,KAAK,GAAG,6BAA6B,4BAC5C;gCACF,aAAY;gCACZ,QAAQ;;;;;;4BAET,OAAO,KAAK,kBAAI,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,KAAK;;;;;;;;;;;;kCAGzE,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAmB,WAAU;0CAA+C;;;;;;0CAG3F,6LAAC;gCACC,MAAK;gCACL,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,gBAAgB;gCAChC,UAAU;gCACV,WAAW,CAAC,qJAAqJ,EAC/J,OAAO,gBAAgB,GAAG,6BAA6B,4BACvD;gCACF,aAAY;gCACZ,QAAQ;;;;;;4BAET,OAAO,gBAAgB,kBAAI,6LAAC;gCAAE,WAAU;0CAA6B,OAAO,gBAAgB;;;;;;;;;;;;kCAG/F,6LAAC;;0CACC,6LAAC;gCAAM,SAAQ;gCAAc,WAAU;0CAA+C;;;;;;0CAGtF,6LAAC;gCACC,IAAG;gCACH,MAAK;gCACL,OAAO,SAAS,WAAW;gCAC3B,UAAU;gCACV,aAAY;gCACZ,MAAM;gCACN,WAAU;;;;;;;;;;;;kCAId,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,IAAG;oCACH,MAAK;oCACL,MAAK;oCACL,SAAS,SAAS,YAAY;oCAC9B,UAAU;oCACV,WAAU;;;;;;;;;;;0CAGd,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAM,SAAQ;oCAAe,WAAU;8CAA2C;;;;;;;;;;;;;;;;;kCAMvF,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,MAAK;4BACL,UAAU;4BACV,WAAW,CAAC,sEAAsE,EAChF,eACI,8CACA,mGACJ;sCAED,6BACC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;wCAA6C,OAAM;wCAA6B,MAAK;wCAAO,SAAQ;;0DACjH,6LAAC;gDAAO,WAAU;gDAAa,IAAG;gDAAK,IAAG;gDAAK,GAAE;gDAAK,QAAO;gDAAe,aAAY;;;;;;0DACxF,6LAAC;gDAAK,WAAU;gDAAa,MAAK;gDAAe,GAAE;;;;;;;;;;;;oCAC/C;;;;;;uCAIR;;;;;;;;;;;kCAKN,6LAAC;wBAAE,WAAU;;4BAAyC;0CAEpD,6LAAC;;;;;4BAAK;0CACe,6LAAC;gCAAE,MAAK;gCAA8B,WAAU;0CAA0E;;;;;;;;;;;;;;;;;;;;;;;;AAKzJ;GA3VwB;KAAA"}}, {"offset": {"line": 1552, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1558, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/junior-academy/JuniorAcademyClientContent.tsx"], "sourcesContent": ["'use client';\n\nimport Header from \"@/components/Header\";\nimport Footer from \"@/components/Footer\";\nimport Image from \"next/image\";\nimport Link from \"next/link\";\nimport dynamic from 'next/dynamic';\nimport DirectRegistrationForm from '../../../components/DirectRegistrationForm';\n\nconst ParticleSection = dynamic(() => import('@/components/ParticleSection'), { ssr: false });\nconst TestForm = dynamic(() => import('../../../components/TestForm'), { ssr: false });\n\nexport default function JuniorAcademyClientContent() {\n  return (\n    <>\n      <Header />\n      <main>\n        {/* Hero Section */}\n        <section className=\"relative min-h-[60vh] flex items-center\">\n          {/* Background Image */}\n          <div className=\"absolute inset-0 z-0\">\n            <Image\n              src=\"/junior-academy.jpg\"\n              alt=\"Children playing football\"\n              fill\n              priority\n              className=\"object-cover brightness-75\"\n            />\n          </div>\n\n          {/* Overlay for better text contrast */}\n          <div className=\"absolute inset-0 bg-gradient-to-r from-black/50 via-black/30 to-transparent z-1\"></div>\n\n          <div className=\"container relative z-10 text-white\">\n            <div className=\"max-w-2xl\">\n              <h1 className=\"text-4xl md:text-5xl font-bold mb-4 leading-tight\">\n                Junior Academy\n              </h1>\n              <p className=\"text-xl mb-6\">\n                Developing the next generation of football stars through fun,\n                engaging, and professional coaching.\n              </p>\n              <Link\n                href=\"#register\"\n                className=\"bg-yellow-400 text-primary-900 px-6 py-3 rounded-lg font-semibold hover:bg-yellow-300 transition-colors shadow-lg inline-flex items-center\"\n              >\n                Register Now\n                <svg className=\"w-5 h-5 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M14 5l7 7m0 0l-7 7m7-7H3\"></path>\n                </svg>\n              </Link>\n            </div>\n          </div>\n        </section>\n\n        {/* Program Overview Section */}\n        <section className=\"py-16\">\n          <ParticleSection className=\"container\">\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-12 items-center\">\n              <div>\n                <h2 className=\"text-3xl font-bold mb-6\">Program Overview</h2>\n                <p className=\"mb-4\">\n                  The Northern Nepalese United FC Junior Academy is designed to introduce children\n                  ages 5-15 to the beautiful game of football in a fun, safe, and supportive environment.\n                  Our program focuses on developing fundamental skills, fostering teamwork, and building\n                  confidence both on and off the pitch.\n                </p>\n                <p>\n                  Led by qualified coaches with experience in youth development, our structured training\n                  sessions are tailored to different age groups and skill levels, ensuring every child\n                  can progress at their own pace while enjoying the game.\n                </p>\n              </div>\n              <div className=\"relative h-80 rounded-lg overflow-hidden shadow-lg\">\n                <Image\n                  src=\"/junior-training.jpg\"\n                  alt=\"Junior football training session\"\n                  fill\n                  className=\"object-cover\"\n                />\n              </div>\n            </div>\n          </ParticleSection>\n        </section>\n\n        {/* Program Details Section */}\n        <section className=\"py-16 bg-neutral-50\">\n          <div className=\"container\">\n            <h2 className=\"text-3xl font-bold mb-12 text-center\">Program Details</h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8\">\n              {/* Age Groups */}\n              <div className=\"bg-white p-8 rounded-lg shadow-md\">\n                <div className=\"bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mb-6 mx-auto\">\n                  <svg className=\"w-8 h-8 text-primary-800\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z\"></path>\n                  </svg>\n                </div>\n                <h3 className=\"text-xl font-bold mb-4 text-center\">Age Groups</h3>\n                <ul className=\"space-y-2\">\n                  <li className=\"flex items-center\">\n                    <span className=\"bg-primary-100 text-primary-800 rounded-full p-1 mr-3\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </span>\n                    <span>Little Stars: Ages 5-7</span>\n                  </li>\n                  <li className=\"flex items-center\">\n                    <span className=\"bg-primary-100 text-primary-800 rounded-full p-1 mr-3\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </span>\n                    <span>Junior Developers: Ages 8-11</span>\n                  </li>\n                  <li className=\"flex items-center\">\n                    <span className=\"bg-primary-100 text-primary-800 rounded-full p-1 mr-3\">\n                      <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-4 w-4\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                        <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                      </svg>\n                    </span>\n                    <span>Youth Talents: Ages 12-15</span>\n                  </li>\n                </ul>\n              </div>\n\n              {/* Training Schedule */}\n              <div className=\"bg-white p-8 rounded-lg shadow-md\">\n                <div className=\"bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mb-6 mx-auto\">\n                  <svg className=\"w-8 h-8 text-primary-800\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"></path>\n                  </svg>\n                </div>\n                <h3 className=\"text-xl font-bold mb-4 text-center\">Training Schedule</h3>\n                <ul className=\"space-y-3\">\n                  <li className=\"mb-2\">\n                    <p className=\"font-semibold text-primary-700\">Little Stars (5-7):</p>\n                    <p>Saturdays, 9:00 AM - 10:00 AM</p>\n                  </li>\n                  <li className=\"mb-2\">\n                    <p className=\"font-semibold text-primary-700\">Junior Developers (8-11):</p>\n                    <p>Saturdays, 10:15 AM - 11:30 AM</p>\n                  </li>\n                  <li>\n                    <p className=\"font-semibold text-primary-700\">Youth Talents (12-15):</p>\n                    <p>Saturdays, 11:45 AM - 1:15 PM</p>\n                  </li>\n                </ul>\n              </div>\n\n              {/* Location */}\n              <div className=\"bg-white p-8 rounded-lg shadow-md\">\n                <div className=\"bg-primary-100 w-16 h-16 rounded-full flex items-center justify-center mb-6 mx-auto\">\n                  <svg className=\"w-8 h-8 text-primary-800\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\"></path>\n                    <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\"></path>\n                  </svg>\n                </div>\n                <h3 className=\"text-xl font-bold mb-4 text-center\">Location</h3>\n                <p className=\"mb-4 text-center\">\n                  John Oxley Reserve Field 2\n                  <br />\n                  2 Ogg Rd, Murrumba Downs QLD 4503\n                </p>\n                <div className=\"text-center\">\n                  <a\n                    href=\"https://maps.google.com/?q=2+Ogg+Rd,+Murrumba+Downs+QLD+4503\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    className=\"inline-flex items-center text-primary-600 font-semibold hover:text-primary-800 transition-colors\"\n                  >\n                    View on Map\n                    <svg className=\"w-4 h-4 ml-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14\"></path>\n                    </svg>\n                  </a>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Benefits Section */}\n        <section className=\"py-16\">\n          <div className=\"container\">\n            <h2 className=\"text-3xl font-bold mb-12 text-center\">Benefits of Joining Our Academy</h2>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 max-w-5xl mx-auto\">\n              <div className=\"bg-white border border-gray-100 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow\">\n                <div className=\"flex items-start\">\n                  <div className=\"bg-primary-100 p-3 rounded-full mr-4\">\n                    <svg className=\"w-6 h-6 text-primary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M13 10V3L4 14h7v7l9-11h-7z\"></path>\n                    </svg>\n                  </div>\n                  <div>\n                    <h3 className=\"text-xl font-bold mb-2\">Skill Development</h3>\n                    <p className=\"text-gray-600\">\n                      Structured training focusing on technical skills such as dribbling, passing, shooting, and game awareness.\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white border border-gray-100 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow\">\n                <div className=\"flex items-start\">\n                  <div className=\"bg-primary-100 p-3 rounded-full mr-4\">\n                    <svg className=\"w-6 h-6 text-primary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z\"></path>\n                    </svg>\n                  </div>\n                  <div>\n                    <h3 className=\"text-xl font-bold mb-2\">Teamwork & Social Skills</h3>\n                    <p className=\"text-gray-600\">\n                      Learning to work with others, communicate effectively, and build lasting friendships.\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white border border-gray-100 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow\">\n                <div className=\"flex items-start\">\n                  <div className=\"bg-primary-100 p-3 rounded-full mr-4\">\n                    <svg className=\"w-6 h-6 text-primary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z\"></path>\n                    </svg>\n                  </div>\n                  <div>\n                    <h3 className=\"text-xl font-bold mb-2\">Physical Health</h3>\n                    <p className=\"text-gray-600\">\n                      Regular exercise that improves cardiovascular health, coordination, strength, and overall fitness.\n                    </p>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"bg-white border border-gray-100 p-6 rounded-lg shadow-sm hover:shadow-md transition-shadow\">\n                <div className=\"flex items-start\">\n                  <div className=\"bg-primary-100 p-3 rounded-full mr-4\">\n                    <svg className=\"w-6 h-6 text-primary-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z\"></path>\n                    </svg>\n                  </div>\n                  <div>\n                    <h3 className=\"text-xl font-bold mb-2\">Confidence Building</h3>\n                    <p className=\"text-gray-600\">\n                      Developing self-esteem through mastering new skills and overcoming challenges in a supportive environment.\n                    </p>\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Coaches Section */}\n        <section className=\"py-16 bg-primary-900 text-white\">\n          <div className=\"container\">\n            <h2 className=\"text-3xl font-bold mb-12 text-center\">Our Coaching Team</h2>\n\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-8 max-w-5xl mx-auto\">\n              {/* Coach 1 */}\n              <div className=\"bg-primary-800/60 p-6 rounded-xl text-center\">\n                <div className=\"w-32 h-32 rounded-full bg-neutral-200 mx-auto mb-4 relative overflow-hidden\">\n                  <div className=\"absolute inset-0 flex items-center justify-center text-neutral-500\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-16 w-16\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                    </svg>\n                  </div>\n                </div>\n                <h3 className=\"text-xl font-bold\">Raj Sharma</h3>\n                <p className=\"text-yellow-400 font-medium mb-2\">Head Coach</p>\n                <p className=\"text-gray-300 text-sm\">\n                  FA Level 2 Coach with 10+ years experience in youth development\n                </p>\n              </div>\n\n              {/* Coach 2 */}\n              <div className=\"bg-primary-800/60 p-6 rounded-xl text-center\">\n                <div className=\"w-32 h-32 rounded-full bg-neutral-200 mx-auto mb-4 relative overflow-hidden\">\n                  <div className=\"absolute inset-0 flex items-center justify-center text-neutral-500\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-16 w-16\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                    </svg>\n                  </div>\n                </div>\n                <h3 className=\"text-xl font-bold\">Sita Gurung</h3>\n                <p className=\"text-yellow-400 font-medium mb-2\">Youth Coach</p>\n                <p className=\"text-gray-300 text-sm\">\n                  Specialized in coaching children aged 5-11, with background in early childhood education\n                </p>\n              </div>\n\n              {/* Coach 3 */}\n              <div className=\"bg-primary-800/60 p-6 rounded-xl text-center\">\n                <div className=\"w-32 h-32 rounded-full bg-neutral-200 mx-auto mb-4 relative overflow-hidden\">\n                  <div className=\"absolute inset-0 flex items-center justify-center text-neutral-500\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-16 w-16\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={1} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n                    </svg>\n                  </div>\n                </div>\n                <h3 className=\"text-xl font-bold\">Anil Thapa</h3>\n                <p className=\"text-yellow-400 font-medium mb-2\">Technical Coach</p>\n                <p className=\"text-gray-300 text-sm\">\n                  Former professional player focusing on advanced skills for older youth players\n                </p>\n              </div>\n            </div>\n\n            <div className=\"mt-12 text-center\">\n              <p className=\"text-lg max-w-3xl mx-auto\">\n                All our coaches hold current Working with Children Checks and First Aid certifications. They are passionate about youth development both on and off the pitch.\n              </p>\n            </div>\n          </div>\n        </section>\n\n        {/* FAQ Section */}\n        <section className=\"py-16 bg-neutral-50\">\n          <div className=\"container\">\n            <h2 className=\"text-3xl font-bold mb-12 text-center\">Frequently Asked Questions</h2>\n\n            <div className=\"max-w-3xl mx-auto space-y-6\">\n              <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n                <h3 className=\"text-xl font-bold mb-3\">What equipment does my child need?</h3>\n                <p className=\"text-gray-700\">\n                  Players should wear comfortable sports clothes, shin guards, and football boots (sneakers are acceptable for the youngest age group). Each player should bring their own water bottle. Training bibs and balls are provided by the club.\n                </p>\n              </div>\n\n              <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n                <h3 className=\"text-xl font-bold mb-3\">Do you offer trial sessions?</h3>\n                <p className=\"text-gray-700\">\n                  Yes, new players are welcome to attend one free trial session before committing to the program. Please contact us in advance to arrange this.\n                </p>\n              </div>\n\n              <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n                <h3 className=\"text-xl font-bold mb-3\">What happens if it rains?</h3>\n                <p className=\"text-gray-700\">\n                  In case of light rain, training will usually continue. For heavy rain or unsafe conditions, sessions may be canceled. We will notify parents via email and SMS as early as possible.\n                </p>\n              </div>\n\n              <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n                <h3 className=\"text-xl font-bold mb-3\">Are parents required to stay during training?</h3>\n                <p className=\"text-gray-700\">\n                  Parents of children under 8 are required to remain at the venue during sessions. For older children, parents may leave but must be contactable by phone and return promptly at the end of the session.\n                </p>\n              </div>\n\n              <div className=\"bg-white p-6 rounded-lg shadow-sm\">\n                <h3 className=\"text-xl font-bold mb-3\">How much does it cost?</h3>\n                <p className=\"text-gray-700\">\n                  The program costs $150 per term (10 weeks) with discounts available for siblings. This includes weekly training sessions, a club t-shirt, and end-of-term participation certificate.\n                </p>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Registration Section */}\n        <section id=\"register\" className=\"py-16 scroll-mt-24\">\n          <div className=\"container\">\n            <div className=\"bg-white rounded-xl shadow-lg overflow-hidden max-w-5xl mx-auto\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2\">\n                <div className=\"bg-primary-800 p-8 md:p-12 text-white\">\n                  <h2 className=\"text-3xl font-bold mb-6\">Register Your Child</h2>\n                  <p className=\"mb-4\">\n                    Join our Junior Academy today and give your child the opportunity to develop football skills in a fun, supportive environment.\n                  </p>\n                  <ul className=\"space-y-3 mb-8\">\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 mr-3 text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\"></path>\n                      </svg>\n                      10-week training programs\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 mr-3 text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\"></path>\n                      </svg>\n                      Professional coaching staff\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 mr-3 text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\"></path>\n                      </svg>\n                      Age-appropriate training methods\n                    </li>\n                    <li className=\"flex items-center\">\n                      <svg className=\"w-5 h-5 mr-3 text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\" xmlns=\"http://www.w3.org/2000/svg\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M5 13l4 4L19 7\"></path>\n                      </svg>\n                      Free club t-shirt included\n                    </li>\n                  </ul>\n                  <p className=\"text-sm text-gray-300\">\n                    Next term starts: February 3, 2024\n                  </p>\n                </div>\n                <div className=\"p-8 md:p-12\">\n                  <h3 className=\"text-2xl font-bold mb-6 text-primary-800\">Registration Form</h3>\n\n                  {/* Simple inline test */}\n                  <div className=\"mb-6 p-4 border border-blue-500 bg-blue-50\">\n                    <h4 className=\"font-bold text-blue-800 mb-2\">INLINE TEST FORM</h4>\n                    <input\n                      type=\"text\"\n                      placeholder=\"Type here - inline test\"\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded bg-white\"\n                      style={{ backgroundColor: 'white !important', pointerEvents: 'auto' }}\n                    />\n                  </div>\n\n                  <TestForm />\n                  <div className=\"mt-6\">\n                    <DirectRegistrationForm />\n                  </div>\n                </div>\n              </div>\n            </div>\n          </div>\n        </section>\n\n        {/* Call to Action Section */}\n        <section className=\"bg-gradient-to-r from-primary-800 to-primary-900 py-10\">\n          <div className=\"container\">\n            <div className=\"flex flex-col md:flex-row items-center justify-between max-w-5xl mx-auto\">\n              <div className=\"text-white mb-6 md:mb-0 text-center md:text-left\">\n                <h2 className=\"text-2xl font-bold mb-2\">Have Questions?</h2>\n                <p className=\"text-sm text-gray-300 max-w-xl\">\n                  Contact our Junior Academy coordinator for more information about our programs.\n                </p>\n              </div>\n              <div className=\"flex gap-3\">\n                <Link\n                  href=\"/contact\"\n                  className=\"bg-white text-primary-800 px-5 py-2 rounded-lg font-semibold hover:bg-yellow-400 transition-colors shadow-md text-sm\"\n                >\n                  Contact Us\n                </Link>\n                <a\n                  href=\"tel:+61412345678\"\n                  className=\"bg-transparent border-2 border-white text-white px-5 py-2 rounded-lg font-semibold hover:bg-white hover:text-primary-800 transition-colors text-sm\"\n                >\n                  Call: 0412 345 678\n                </a>\n              </div>\n            </div>\n          </div>\n        </section>\n      </main>\n      <Footer />\n    </>\n  );\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AASA,MAAM,kBAAkB,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IAAgD,KAAK;;KAA/E;AACN,MAAM,WAAW,CAAA,GAAA,kKAAA,CAAA,UAAO,AAAD,EAAE;;;;;;IAAgD,KAAK;;MAAxE;AAES,SAAS;IACtB,qBACE;;0BACE,6LAAC,wHAAA,CAAA,UAAM;;;;;0BACP,6LAAC;;kCAEC,6LAAC;wBAAQ,WAAU;;0CAEjB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,IAAI;oCACJ,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAKd,6LAAC;gCAAI,WAAU;;;;;;0CAEf,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAAoD;;;;;;sDAGlE,6LAAC;4CAAE,WAAU;sDAAe;;;;;;sDAI5B,6LAAC,+JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;;gDACX;8DAEC,6LAAC;oDAAI,WAAU;oDAAe,MAAK;oDAAO,QAAO;oDAAe,SAAQ;oDAAY,OAAM;8DACxF,cAAA,6LAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAY;wDAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ/E,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAgB,WAAU;sCACzB,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA0B;;;;;;0DACxC,6LAAC;gDAAE,WAAU;0DAAO;;;;;;0DAMpB,6LAAC;0DAAE;;;;;;;;;;;;kDAML,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4CACJ,KAAI;4CACJ,KAAI;4CACJ,IAAI;4CACJ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQpB,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAErD,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAA2B,MAAK;wDAAO,QAAO;wDAAe,SAAQ;wDAAY,OAAM;kEACpG,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAG,WAAU;8DAAqC;;;;;;8DACnD,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAK,WAAU;8EACd,cAAA,6LAAC;wEAAI,OAAM;wEAA6B,WAAU;wEAAU,SAAQ;wEAAY,MAAK;kFACnF,cAAA,6LAAC;4EAAK,UAAS;4EAAU,GAAE;4EAAqH,UAAS;;;;;;;;;;;;;;;;8EAG7J,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAK,WAAU;8EACd,cAAA,6LAAC;wEAAI,OAAM;wEAA6B,WAAU;wEAAU,SAAQ;wEAAY,MAAK;kFACnF,cAAA,6LAAC;4EAAK,UAAS;4EAAU,GAAE;4EAAqH,UAAS;;;;;;;;;;;;;;;;8EAG7J,6LAAC;8EAAK;;;;;;;;;;;;sEAER,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAK,WAAU;8EACd,cAAA,6LAAC;wEAAI,OAAM;wEAA6B,WAAU;wEAAU,SAAQ;wEAAY,MAAK;kFACnF,cAAA,6LAAC;4EAAK,UAAS;4EAAU,GAAE;4EAAqH,UAAS;;;;;;;;;;;;;;;;8EAG7J,6LAAC;8EAAK;;;;;;;;;;;;;;;;;;;;;;;;sDAMZ,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAA2B,MAAK;wDAAO,QAAO;wDAAe,SAAQ;wDAAY,OAAM;kEACpG,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAY;4DAAI,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAG,WAAU;8DAAqC;;;;;;8DACnD,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAE,WAAU;8EAAiC;;;;;;8EAC9C,6LAAC;8EAAE;;;;;;;;;;;;sEAEL,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAE,WAAU;8EAAiC;;;;;;8EAC9C,6LAAC;8EAAE;;;;;;;;;;;;sEAEL,6LAAC;;8EACC,6LAAC;oEAAE,WAAU;8EAAiC;;;;;;8EAC9C,6LAAC;8EAAE;;;;;;;;;;;;;;;;;;;;;;;;sDAMT,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAA2B,MAAK;wDAAO,QAAO;wDAAe,SAAQ;wDAAY,OAAM;;0EACpG,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;0EACrE,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAG,WAAU;8DAAqC;;;;;;8DACnD,6LAAC;oDAAE,WAAU;;wDAAmB;sEAE9B,6LAAC;;;;;wDAAK;;;;;;;8DAGR,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,MAAK;wDACL,QAAO;wDACP,KAAI;wDACJ,WAAU;;4DACX;0EAEC,6LAAC;gEAAI,WAAU;gEAAe,MAAK;gEAAO,QAAO;gEAAe,SAAQ;gEAAY,OAAM;0EACxF,cAAA,6LAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAUnF,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAErD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAA2B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;4DAAY,OAAM;sEACpG,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAyB;;;;;;0EACvC,6LAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;sDAOnC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAA2B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;4DAAY,OAAM;sEACpG,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAyB;;;;;;0EACvC,6LAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;sDAOnC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAA2B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;4DAAY,OAAM;sEACpG,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAyB;;;;;;0EACvC,6LAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;sDAOnC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,WAAU;4DAA2B,MAAK;4DAAO,QAAO;4DAAe,SAAQ;4DAAY,OAAM;sEACpG,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAY;gEAAI,GAAE;;;;;;;;;;;;;;;;kEAGzE,6LAAC;;0EACC,6LAAC;gEAAG,WAAU;0EAAyB;;;;;;0EACvC,6LAAC;gEAAE,WAAU;0EAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAWzC,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAErD,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAAY,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEACnG,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,6LAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,6LAAC;oDAAE,WAAU;8DAAmC;;;;;;8DAChD,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAMvC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAAY,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEACnG,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,6LAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,6LAAC;oDAAE,WAAU;8DAAmC;;;;;;8DAChD,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAMvC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAAY,MAAK;4DAAO,SAAQ;4DAAY,QAAO;sEACnG,cAAA,6LAAC;gEAAK,eAAc;gEAAQ,gBAAe;gEAAQ,aAAa;gEAAG,GAAE;;;;;;;;;;;;;;;;;;;;;8DAI3E,6LAAC;oDAAG,WAAU;8DAAoB;;;;;;8DAClC,6LAAC;oDAAE,WAAU;8DAAmC;;;;;;8DAChD,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;8CAMzC,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAE,WAAU;kDAA4B;;;;;;;;;;;;;;;;;;;;;;kCAQ/C,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;8CAErD,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAyB;;;;;;8DACvC,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAK/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAyB;;;;;;8DACvC,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAK/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAyB;;;;;;8DACvC,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAK/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAyB;;;;;;8DACvC,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;sDAK/B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAyB;;;;;;8DACvC,6LAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASrC,6LAAC;wBAAQ,IAAG;wBAAW,WAAU;kCAC/B,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA0B;;;;;;8DACxC,6LAAC;oDAAE,WAAU;8DAAO;;;;;;8DAGpB,6LAAC;oDAAG,WAAU;;sEACZ,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAI,WAAU;oEAA+B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;oEAAY,OAAM;8EACxG,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;gEACjE;;;;;;;sEAGR,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAI,WAAU;oEAA+B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;oEAAY,OAAM;8EACxG,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;gEACjE;;;;;;;sEAGR,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAI,WAAU;oEAA+B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;oEAAY,OAAM;8EACxG,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;gEACjE;;;;;;;sEAGR,6LAAC;4DAAG,WAAU;;8EACZ,6LAAC;oEAAI,WAAU;oEAA+B,MAAK;oEAAO,QAAO;oEAAe,SAAQ;oEAAY,OAAM;8EACxG,cAAA,6LAAC;wEAAK,eAAc;wEAAQ,gBAAe;wEAAQ,aAAY;wEAAI,GAAE;;;;;;;;;;;gEACjE;;;;;;;;;;;;;8DAIV,6LAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAIvC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAA2C;;;;;;8DAGzD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAG,WAAU;sEAA+B;;;;;;sEAC7C,6LAAC;4DACC,MAAK;4DACL,aAAY;4DACZ,WAAU;4DACV,OAAO;gEAAE,iBAAiB;gEAAoB,eAAe;4DAAO;;;;;;;;;;;;8DAIxE,6LAAC;;;;;8DACD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,wIAAA,CAAA,UAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASnC,6LAAC;wBAAQ,WAAU;kCACjB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA0B;;;;;;0DACxC,6LAAC;gDAAE,WAAU;0DAAiC;;;;;;;;;;;;kDAIhD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQX,6LAAC,wHAAA,CAAA,UAAM;;;;;;;AAGb;MA9bwB"}}, {"offset": {"line": 3298, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}