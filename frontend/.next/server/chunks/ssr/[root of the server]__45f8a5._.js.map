{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyR,GACtT,uDACA"}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqQ,GAClS,mCACA"}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Footer.tsx"], "sourcesContent": ["import Link from 'next/link'\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear()\n\n  return (\n    <footer className=\"bg-gradient-to-b from-neutral-800 to-neutral-900 text-white\">\n      <div className=\"container py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div className=\"md:col-span-2\">\n            <h3 className=\"text-xl font-bold mb-4 text-white\">Northern Nepalese United FC</h3>\n            <p className=\"mb-4 text-neutral-300\">\n              Brisbane-based Nepalese football club est. 2023, bringing together the community through the beautiful game.\n            </p>\n            <div className=\"flex space-x-4 mt-6\">\n              <a\n                href=\"https://facebook.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                aria-label=\"Facebook\"\n                className=\"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"currentColor\"\n                >\n                  <path d=\"M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z\" />\n                </svg>\n              </a>\n              <a\n                href=\"https://twitter.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                aria-label=\"Twitter\"\n                className=\"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"currentColor\"\n                >\n                  <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\" />\n                </svg>\n              </a>\n              <a\n                href=\"https://instagram.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                aria-label=\"Instagram\"\n                className=\"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"currentColor\"\n                >\n                  <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          <div>\n            <h3 className=\"text-xl font-bold mb-4 text-white\">Club Links</h3>\n            <ul className=\"space-y-2 text-neutral-300\">\n              <li>\n                <Link href=\"/about\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  About Us\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/team\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  Our Team\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/news\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  News\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/events\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  Events & Fixtures\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/gallery\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  Gallery\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"text-xl font-bold mb-4 text-white\">Contact</h3>\n            <address className=\"not-italic text-neutral-300\">\n              <p className=\"mb-2 flex items-start\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 text-neutral-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                </svg>\n                John Oxley Reserve<br />Murrumba Downs<br />Brisbane, QLD\n              </p>\n              <p className=\"mb-2 flex items-start\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 text-neutral-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                </svg>\n                <a href=\"tel:+61424770570\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  0424 770 570\n                </a>\n              </p>\n              <p className=\"flex items-start\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 text-neutral-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                </svg>\n                <a\n                  href=\"mailto:<EMAIL>\"\n                  className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\"\n                >\n                  <EMAIL>\n                </a>\n              </p>\n            </address>\n          </div>\n        </div>\n\n        <div className=\"border-t border-neutral-700 mt-10 pt-8 text-center text-neutral-400\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p>&copy; {currentYear} Northern Nepalese United - NNUFC. All rights reserved.</p>\n            <ul className=\"flex space-x-4 mt-4 md:mt-0\">\n              <li>\n                <Link href=\"/sponsors\" className=\"text-neutral-400 hover:text-white hover:underline transition-colors no-underline\">\n                  Sponsors\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-neutral-400 hover:text-white hover:underline transition-colors no-underline\">\n                  Contact\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,cAAW;4CACX,WAAU;sDAEV,cAAA,8OAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;0DAEL,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,cAAW;4CACX,WAAU;sDAEV,cAAA,8OAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;0DAEL,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,cAAW;4CACX,WAAU;sDAEV,cAAA,8OAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;0DAEL,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmF;;;;;;;;;;;sDAInH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmF;;;;;;;;;;;sDAIlH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmF;;;;;;;;;;;sDAIlH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAmF;;;;;;;;;;;sDAIpH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmF;;;;;;;;;;;;;;;;;;;;;;;sCAOzH,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAQ,WAAU;;sDACjB,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAuC,MAAK;oDAAO,SAAQ;oDAAY,QAAO;;sEAC9H,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;sEACrE,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;gDACjE;8DACY,8OAAC;;;;;gDAAK;8DAAc,8OAAC;;;;;gDAAK;;;;;;;sDAE9C,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAuC,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAC9H,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,8OAAC;oDAAE,MAAK;oDAAmB,WAAU;8DAAmF;;;;;;;;;;;;sDAI1H,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAuC,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAC9H,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,8OAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQT,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAE;oCAAQ;oCAAY;;;;;;;0CACvB,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAY,WAAU;sDAAmF;;;;;;;;;;;kDAItH,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAmF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnI"}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/lib/sanity.ts"], "sourcesContent": ["import { createClient } from 'next-sanity'\nimport imageUrlBuilder from '@sanity/image-url'\n\nconst projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!\nconst dataset = process.env.NEXT_PUBLIC_SANITY_DATASET!\nconst apiVersion = process.env.NEXT_PUBLIC_SANITY_API_VERSION || '2024-05-23'\n\nexport const client = createClient({\n  projectId,\n  dataset,\n  apiVersion,\n  useCdn: typeof document !== 'undefined',\n})\n\n// Helper function for generating image URLs with the Sanity Image Pipeline\nconst builder = imageUrlBuilder({\n  projectId,\n  dataset,\n})\n\nexport function urlForImage(source: any) {\n  return builder.image(source)\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM;AACN,MAAM,aAAa,kDAA8C;AAE1D,MAAM,SAAS,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE;IACjC;IACA;IACA;IACA,QAAQ,OAAO,aAAa;AAC9B;AAEA,2EAA2E;AAC3E,MAAM,UAAU,CAAA,GAAA,gKAAA,CAAA,UAAe,AAAD,EAAE;IAC9B;IACA;AACF;AAEO,SAAS,YAAY,MAAW;IACrC,OAAO,QAAQ,KAAK,CAAC;AACvB"}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/lib/queries.ts"], "sourcesContent": ["// Latest news articles\nexport const latestNewsQuery = `\n  *[_type == \"newsArticle\"] | order(publishedAt desc)[0...3] {\n    _id,\n    title,\n    slug,\n    publishedAt,\n    mainImage,\n    summary\n  }\n`;\n\n// Single news article by slug\nexport const newsArticleBySlugQuery = `\n  *[_type == \"newsArticle\" && slug.current == $slug][0] {\n    _id,\n    title,\n    slug,\n    publishedAt,\n    mainImage,\n    summary,\n    body\n  }\n`;\n\n// All news articles\nexport const allNewsArticlesQuery = `\n  *[_type == \"newsArticle\"] | order(publishedAt desc) {\n    _id,\n    title,\n    slug,\n    publishedAt,\n    mainImage,\n    summary\n  }\n`;\n\n// Upcoming events\nexport const upcomingEventsQuery = `\n  *[_type == \"event\" && date > now()] | order(date asc)[0...5] {\n    _id,\n    title,\n    slug,\n    date,\n    location,\n    eventType,\n    opponent,\n    homeOrAway\n  }\n`;\n\n// All events\nexport const allEventsQuery = `\n  *[_type == \"event\"] | order(date desc) {\n    _id,\n    title,\n    slug,\n    date,\n    location,\n    eventType,\n    opponent,\n    homeOrAway,\n    result\n  }\n`;\n\n// Single event by slug\nexport const eventBySlugQuery = `\n  *[_type == \"event\" && slug.current == $slug][0] {\n    _id,\n    title,\n    slug,\n    date,\n    location,\n    eventType,\n    opponent,\n    homeOrAway,\n    result,\n    description\n  }\n`;\n\n// All sponsors ordered by display order\nexport const sponsorsQuery = `\n  *[_type == \"sponsor\"] | order(displayOrder asc) {\n    _id,\n    name,\n    logo,\n    websiteUrl,\n    sponsorshipLevel\n  }\n`;\n\n// Gallery images\nexport const galleryImagesQuery = `\n  *[_type == \"galleryImage\"] | order(dateTaken desc) {\n    _id,\n    title,\n    imageFile,\n    dateTaken\n  }\n`;\n\n// Players\nexport const playersQuery = `\n  *[_type == \"player\"] | order(role desc, jerseyNumber asc) {\n    _id,\n    name,\n    position,\n    role,\n    jerseyNumber,\n    image,\n    stats,\n    bio\n  }\n`;\n\n// Staff\nexport const staffQuery = `\n  *[_type == \"staff\"] | order(displayOrder asc) {\n    _id,\n    name,\n    role,\n    image,\n    bio,\n    contactInfo\n  }\n`;\n"], "names": [], "mappings": "AAAA,uBAAuB;;;;;;;;;;;;;AAChB,MAAM,kBAAkB,CAAC;;;;;;;;;AAShC,CAAC;AAGM,MAAM,yBAAyB,CAAC;;;;;;;;;;AAUvC,CAAC;AAGM,MAAM,uBAAuB,CAAC;;;;;;;;;AASrC,CAAC;AAGM,MAAM,sBAAsB,CAAC;;;;;;;;;;;AAWpC,CAAC;AAGM,MAAM,iBAAiB,CAAC;;;;;;;;;;;;AAY/B,CAAC;AAGM,MAAM,mBAAmB,CAAC;;;;;;;;;;;;;AAajC,CAAC;AAGM,MAAM,gBAAgB,CAAC;;;;;;;;AAQ9B,CAAC;AAGM,MAAM,qBAAqB,CAAC;;;;;;;AAOnC,CAAC;AAGM,MAAM,eAAe,CAAC;;;;;;;;;;;AAW7B,CAAC;AAGM,MAAM,aAAa,CAAC;;;;;;;;;AAS3B,CAAC"}}, {"offset": {"line": 692, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 698, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/team/page.tsx"], "sourcesContent": ["import Header from \"@/components/Header\";\nimport Footer from \"@/components/Footer\";\nimport Image from \"next/image\";\nimport { client } from \"@/lib/sanity\";\nimport { urlForImage } from \"@/lib/sanity\";\nimport { playersQuery, staffQuery } from \"@/lib/queries\";\n\nexport const metadata = {\n  title: \"Our Team | Northern Nepalese United FC\",\n  description: \"Meet the players and coaching staff of Northern Nepalese United FC.\",\n};\n\n// This makes the page dynamic and will revalidate every 60 seconds\nexport const revalidate = 60;\n\n// Define types for player and staff\ninterface PlayerStats {\n  appearances: number;\n  goals?: number;\n  assists?: number;\n  cleanSheets?: number;\n}\n\ninterface SanityImageReference {\n  _type: string;\n  asset: {\n    _ref: string;\n    _type: string;\n  };\n}\n\ninterface Player {\n  _id: string;\n  name: string;\n  position: string;\n  role: string;\n  jerseyNumber: number;\n  image?: SanityImageReference;\n  stats: PlayerStats;\n  bio?: string;\n}\n\ninterface Staff {\n  _id: string;\n  name: string;\n  role: string;\n  image?: SanityImageReference;\n  bio?: string;\n  contactInfo?: {\n    email?: string;\n    phone?: string;\n  };\n}\n\n// Functions to fetch data from Sanity\nasync function getPlayers() {\n  try {\n    return await client.fetch(playersQuery);\n  } catch (error) {\n    console.error(\"Failed to fetch players:\", error);\n    return [];\n  }\n}\n\nasync function getStaff() {\n  try {\n    return await client.fetch(staffQuery);\n  } catch (error) {\n    console.error(\"Failed to fetch staff:\", error);\n    return [];\n  }\n}\n\nexport default async function TeamPage() {\n  // Fetch data from Sanity - this will work once you have data in Sanity\n  // For now, we'll use placeholder data as fallback\n  let players: Player[] = await getPlayers();\n  let staff: Staff[] = await getStaff();\n\n  // Use fallback data if no data is returned from Sanity\n  if (!players || players.length === 0) {\n    players = [\n      {\n        _id: \"1\",\n        name: \"Ram Gurung\",\n        position: \"Forward\",\n        role: \"Captain\",\n        jerseyNumber: 9,\n        stats: {\n          appearances: 15,\n          goals: 8,\n          assists: 3,\n        },\n      },\n      {\n        _id: \"2\",\n        name: \"Suman Tamang\",\n        position: \"Midfielder\",\n        role: \"Player\",\n        jerseyNumber: 10,\n        stats: {\n          appearances: 18,\n          goals: 4,\n          assists: 9,\n        },\n      },\n      {\n        _id: \"3\",\n        name: \"Anish Rai\",\n        position: \"Defender\",\n        role: \"Player\",\n        jerseyNumber: 4,\n        stats: {\n          appearances: 20,\n          goals: 1,\n          assists: 2,\n        },\n      },\n      {\n        _id: \"4\",\n        name: \"Bipin Thapa\",\n        position: \"Goalkeeper\",\n        role: \"Player\",\n        jerseyNumber: 1,\n        stats: {\n          appearances: 17,\n          cleanSheets: 5,\n        },\n      },\n      {\n        _id: \"5\",\n        name: \"Nabin Shrestha\",\n        position: \"Midfielder\",\n        role: \"Player\",\n        jerseyNumber: 8,\n        stats: {\n          appearances: 16,\n          goals: 3,\n          assists: 5,\n        },\n      },\n      {\n        _id: \"6\",\n        name: \"Sanjeev Magar\",\n        position: \"Defender\",\n        role: \"Player\",\n        jerseyNumber: 5,\n        stats: {\n          appearances: 14,\n          goals: 0,\n          assists: 1,\n        },\n      },\n    ];\n  }\n\n  if (!staff || staff.length === 0) {\n    staff = [\n      {\n        _id: \"1\",\n        name: \"Krishna Pun\",\n        role: \"Head Coach\",\n        bio: \"Former professional player with 10+ years of coaching experience.\",\n      },\n      {\n        _id: \"2\",\n        name: \"Binod Khatri\",\n        role: \"Assistant Coach\",\n        bio: \"Specializes in tactical development and player conditioning.\",\n      },\n      {\n        _id: \"3\",\n        name: \"Laxmi Thapa\",\n        role: \"Team Manager\",\n        bio: \"Handles team logistics, scheduling, and administration.\",\n      },\n    ];\n  }\n\n  return (\n    <>\n      <Header />\n      <main>\n        {/* Hero Section */}\n        <section className=\"bg-primary-800 text-white py-16\">\n          <div className=\"container\">\n            <h1 className=\"text-4xl md:text-5xl font-bold mb-4\">Our Team</h1>\n            <p className=\"text-xl max-w-3xl\">\n              Meet the players and coaching staff who represent Northern Nepalese United FC.\n            </p>\n          </div>\n        </section>\n\n        {/* Players Section */}\n        <section className=\"py-16\">\n          <div className=\"container\">\n            <h2 className=\"text-3xl font-bold mb-10 text-center\">Players</h2>\n\n            {/* Captain Section */}\n            {(() => {\n              const captain = players.find(player => player.role === \"Captain\");\n              if (captain) {\n                return (\n                  <div className=\"mb-12\">\n                    <h3 className=\"text-2xl font-bold mb-6 text-center text-primary-600\">Team Captain</h3>\n                    <div className=\"max-w-md mx-auto\">\n                      <div className=\"bg-gradient-to-br from-primary-50 to-primary-100 rounded-lg shadow-lg overflow-hidden border-2 border-primary-200\">\n                        <div className=\"h-48 bg-neutral-100 relative\">\n                          {captain.image ? (\n                            <Image\n                              src={urlForImage(captain.image).url()}\n                              alt={captain.name}\n                              fill\n                              className=\"object-cover\"\n                            />\n                          ) : (\n                            <Image\n                              src=\"/placeholder-player.svg\"\n                              alt={captain.name}\n                              fill\n                              className=\"object-cover\"\n                            />\n                          )}\n                          <div className=\"absolute top-0 right-0 bg-yellow-500 text-white p-3 font-bold text-xl\">\n                            #{captain.jerseyNumber}\n                          </div>\n                          <div className=\"absolute top-0 left-0 bg-primary-600 text-white px-3 py-1 text-sm font-semibold\">\n                            CAPTAIN\n                          </div>\n                        </div>\n\n                        <div className=\"p-6\">\n                          <h4 className=\"text-2xl font-bold mb-2 text-center\">{captain.name}</h4>\n                          <p className=\"text-primary-600 font-medium mb-4 text-center text-lg\">{captain.position}</p>\n\n                          <div className=\"grid grid-cols-3 gap-4 border-t border-primary-200 pt-4 text-center\">\n                            <div>\n                              <p className=\"text-xs text-neutral-600 font-medium\">Appearances</p>\n                              <p className=\"font-bold text-xl text-primary-700\">{captain.stats?.appearances || 0}</p>\n                            </div>\n                            <div>\n                              <p className=\"text-xs text-neutral-600 font-medium\">Goals</p>\n                              <p className=\"font-bold text-xl text-primary-700\">\n                                {captain.position === \"Goalkeeper\" ? \"-\" : captain.stats?.goals || 0}\n                              </p>\n                            </div>\n                            <div>\n                              <p className=\"text-xs text-neutral-600 font-medium\">\n                                {captain.position === \"Goalkeeper\" ? \"Clean Sheets\" : \"Assists\"}\n                              </p>\n                              <p className=\"font-bold text-xl text-primary-700\">\n                                {captain.position === \"Goalkeeper\"\n                                  ? captain.stats?.cleanSheets || 0\n                                  : captain.stats?.assists || 0\n                                }\n                              </p>\n                            </div>\n                          </div>\n\n                          {captain.bio && (\n                            <div className=\"mt-4 pt-4 border-t border-primary-200\">\n                              <p className=\"text-sm text-neutral-700 text-center\">{captain.bio}</p>\n                            </div>\n                          )}\n                        </div>\n                      </div>\n                    </div>\n                  </div>\n                );\n              }\n              return null;\n            })()}\n\n            {/* Other Players Section - Enhanced Design */}\n            {(() => {\n              const otherPlayers = players.filter(player => player.role !== \"Captain\");\n              if (otherPlayers.length > 0) {\n                // Group players by position for better organization\n                const playersByPosition = otherPlayers.reduce((acc, player) => {\n                  const position = player.position;\n                  if (!acc[position]) {\n                    acc[position] = [];\n                  }\n                  acc[position].push(player);\n                  return acc;\n                }, {} as Record<string, Player[]>);\n\n                const positionOrder = [\"Goalkeeper\", \"Defender\", \"Midfielder\", \"Forward\"];\n                const getPositionColor = (position: string) => {\n                  switch (position) {\n                    case \"Goalkeeper\": return \"bg-green-500\";\n                    case \"Defender\": return \"bg-blue-500\";\n                    case \"Midfielder\": return \"bg-purple-500\";\n                    case \"Forward\": return \"bg-red-500\";\n                    default: return \"bg-gray-500\";\n                  }\n                };\n\n                return (\n                  <div>\n                    <h3 className=\"text-3xl font-bold mb-8 text-center\">Squad</h3>\n\n                    {positionOrder.map((position) => {\n                      const positionPlayers = playersByPosition[position];\n                      if (!positionPlayers || positionPlayers.length === 0) return null;\n\n                      return (\n                        <div key={position} className=\"mb-12\">\n                          <div className=\"flex items-center justify-center mb-6\">\n                            <div className={`${getPositionColor(position)} w-4 h-4 rounded-full mr-3`}></div>\n                            <h4 className=\"text-2xl font-bold text-gray-800\">{position}s</h4>\n                            <div className={`${getPositionColor(position)} w-4 h-4 rounded-full ml-3`}></div>\n                          </div>\n\n                          <div className=\"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\">\n                            {positionPlayers.map((player) => (\n                              <div key={player._id} className=\"group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden border border-gray-100\">\n                                {/* Player Image Section */}\n                                <div className=\"relative h-48 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden\">\n                                  {player.image ? (\n                                    <Image\n                                      src={urlForImage(player.image).url()}\n                                      alt={player.name}\n                                      fill\n                                      className=\"object-cover group-hover:scale-110 transition-transform duration-300\"\n                                    />\n                                  ) : (\n                                    <div className=\"w-full h-full flex items-center justify-center bg-gradient-to-br from-primary-100 to-primary-200\">\n                                      <svg className=\"w-16 h-16 text-primary-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"></path>\n                                      </svg>\n                                    </div>\n                                  )}\n\n                                  {/* Jersey Number Badge */}\n                                  <div className={`absolute top-3 left-3 ${getPositionColor(player.position)} text-white text-sm font-bold px-3 py-1 rounded-full shadow-lg`}>\n                                    #{player.jerseyNumber}\n                                  </div>\n\n                                  {/* Position Badge */}\n                                  <div className=\"absolute top-3 right-3 bg-white/90 backdrop-blur-sm text-gray-800 text-xs font-semibold px-2 py-1 rounded-full\">\n                                    {player.position}\n                                  </div>\n\n                                  {/* Vice Captain Badge */}\n                                  {player.role === \"Vice Captain\" && (\n                                    <div className=\"absolute bottom-3 left-3 bg-orange-500 text-white px-2 py-1 text-xs font-semibold rounded-full\">\n                                      VICE CAPTAIN\n                                    </div>\n                                  )}\n                                </div>\n\n                                {/* Player Info Section */}\n                                <div className=\"p-5\">\n                                  <h4 className=\"text-xl font-bold mb-2 text-gray-800 group-hover:text-primary-600 transition-colors\">\n                                    {player.name}\n                                  </h4>\n\n                                  {/* Stats Grid */}\n                                  <div className=\"grid grid-cols-3 gap-3 mt-4\">\n                                    <div className=\"text-center bg-gray-50 rounded-lg p-3 group-hover:bg-primary-50 transition-colors\">\n                                      <p className=\"text-xs text-gray-500 font-medium mb-1\">Apps</p>\n                                      <p className=\"font-bold text-lg text-gray-800\">{player.stats?.appearances || 0}</p>\n                                    </div>\n                                    <div className=\"text-center bg-gray-50 rounded-lg p-3 group-hover:bg-primary-50 transition-colors\">\n                                      <p className=\"text-xs text-gray-500 font-medium mb-1\">\n                                        {player.position === \"Goalkeeper\" ? \"Saves\" : \"Goals\"}\n                                      </p>\n                                      <p className=\"font-bold text-lg text-gray-800\">\n                                        {player.position === \"Goalkeeper\" ? \"-\" : player.stats?.goals || 0}\n                                      </p>\n                                    </div>\n                                    <div className=\"text-center bg-gray-50 rounded-lg p-3 group-hover:bg-primary-50 transition-colors\">\n                                      <p className=\"text-xs text-gray-500 font-medium mb-1\">\n                                        {player.position === \"Goalkeeper\" ? \"Clean\" : \"Assists\"}\n                                      </p>\n                                      <p className=\"font-bold text-lg text-gray-800\">\n                                        {player.position === \"Goalkeeper\"\n                                          ? player.stats?.cleanSheets || 0\n                                          : player.stats?.assists || 0\n                                        }\n                                      </p>\n                                    </div>\n                                  </div>\n\n                                  {/* Bio Preview */}\n                                  {player.bio && (\n                                    <p className=\"text-sm text-gray-600 mt-3 line-clamp-2\">\n                                      {player.bio}\n                                    </p>\n                                  )}\n                                </div>\n                              </div>\n                            ))}\n                          </div>\n                        </div>\n                      );\n                    })}\n                  </div>\n                );\n              }\n              return null;\n            })()}\n          </div>\n        </section>\n\n        {/* Coaching Staff Section - Enhanced Design */}\n        <section className=\"py-16 bg-gradient-to-br from-neutral-50 to-neutral-100\">\n          <div className=\"container\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-4xl font-bold mb-4 text-gray-800\">Coaching Staff</h2>\n              <div className=\"w-24 h-1 bg-primary-600 mx-auto mb-4\"></div>\n              <p className=\"text-gray-600 max-w-2xl mx-auto\">\n                Meet the experienced professionals who guide and develop our players both on and off the field.\n              </p>\n            </div>\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8\">\n              {staff.map((member) => (\n                <div key={member._id} className=\"group bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 overflow-hidden border border-gray-100\">\n                  {/* Staff Image Section */}\n                  <div className=\"relative h-56 bg-gradient-to-br from-gray-100 to-gray-200 overflow-hidden\">\n                    {member.image ? (\n                      <Image\n                        src={urlForImage(member.image).url()}\n                        alt={member.name}\n                        fill\n                        className=\"object-cover group-hover:scale-110 transition-transform duration-300\"\n                      />\n                    ) : (\n                      <div className=\"w-full h-full flex items-center justify-center bg-gradient-to-br from-primary-100 to-primary-200\">\n                        <svg className=\"w-20 h-20 text-primary-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                          <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth=\"2\" d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\"></path>\n                        </svg>\n                      </div>\n                    )}\n\n                    {/* Role Badge */}\n                    <div className=\"absolute top-3 right-3 bg-primary-600 text-white text-xs font-semibold px-3 py-1 rounded-full shadow-lg\">\n                      {member.role}\n                    </div>\n                  </div>\n\n                  {/* Staff Info Section */}\n                  <div className=\"p-6\">\n                    <h3 className=\"text-2xl font-bold mb-2 text-gray-800 group-hover:text-primary-600 transition-colors\">\n                      {member.name}\n                    </h3>\n\n                    {member.bio && (\n                      <p className=\"text-gray-600 mb-4 leading-relaxed\">\n                        {member.bio}\n                      </p>\n                    )}\n\n                    {/* Contact Information */}\n                    {member.contactInfo && (member.contactInfo.email || member.contactInfo.phone) && (\n                      <div className=\"bg-gray-50 rounded-lg p-4 group-hover:bg-primary-50 transition-colors\">\n                        <h4 className=\"text-sm font-semibold text-gray-700 mb-2\">Contact Information</h4>\n                        {member.contactInfo.email && (\n                          <div className=\"flex items-center mb-2\">\n                            <svg className=\"h-4 w-4 mr-3 text-primary-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                            </svg>\n                            <span className=\"text-sm text-gray-600\">{member.contactInfo.email}</span>\n                          </div>\n                        )}\n                        {member.contactInfo.phone && (\n                          <div className=\"flex items-center\">\n                            <svg className=\"h-4 w-4 mr-3 text-primary-600\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                            </svg>\n                            <span className=\"text-sm text-gray-600\">{member.contactInfo.phone}</span>\n                          </div>\n                        )}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </section>\n\n        {/* Join the Team CTA */}\n        <section className=\"py-4 bg-primary-800 text-white\">\n          <div className=\"container\">\n            <div className=\"max-w-4xl mx-auto text-center\">\n              <h2 className=\"text-3xl font-bold mb-4\">Join Our Team</h2>\n              <p className=\"text-lg mb-8\">\n                We&apos;re always looking for new players to join our club. Whether you&apos;re experienced or just starting out, there&apos;s a place for you at Northern Nepalese United FC.\n              </p>\n              <a\n                href=\"/contact\"\n                className=\"bg-white text-primary-700 hover:bg-primary-50 px-8 py-3 rounded-lg font-semibold inline-flex items-center\"\n              >\n                Contact Us to Join\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 ml-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n        </section>\n      </main>\n      <Footer />\n    </>\n  );\n}"], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AAEA;;;;;;;;AAEO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAGO,MAAM,aAAa;AAyC1B,sCAAsC;AACtC,eAAe;IACb,IAAI;QACF,OAAO,MAAM,6GAAA,CAAA,SAAM,CAAC,KAAK,CAAC,8GAAA,CAAA,eAAY;IACxC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,EAAE;IACX;AACF;AAEA,eAAe;IACb,IAAI;QACF,OAAO,MAAM,6GAAA,CAAA,SAAM,CAAC,KAAK,CAAC,8GAAA,CAAA,aAAU;IACtC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,EAAE;IACX;AACF;AAEe,eAAe;IAC5B,uEAAuE;IACvE,kDAAkD;IAClD,IAAI,UAAoB,MAAM;IAC9B,IAAI,QAAiB,MAAM;IAE3B,uDAAuD;IACvD,IAAI,CAAC,WAAW,QAAQ,MAAM,KAAK,GAAG;QACpC,UAAU;YACR;gBACE,KAAK;gBACL,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,cAAc;gBACd,OAAO;oBACL,aAAa;oBACb,OAAO;oBACP,SAAS;gBACX;YACF;YACA;gBACE,KAAK;gBACL,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,cAAc;gBACd,OAAO;oBACL,aAAa;oBACb,OAAO;oBACP,SAAS;gBACX;YACF;YACA;gBACE,KAAK;gBACL,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,cAAc;gBACd,OAAO;oBACL,aAAa;oBACb,OAAO;oBACP,SAAS;gBACX;YACF;YACA;gBACE,KAAK;gBACL,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,cAAc;gBACd,OAAO;oBACL,aAAa;oBACb,aAAa;gBACf;YACF;YACA;gBACE,KAAK;gBACL,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,cAAc;gBACd,OAAO;oBACL,aAAa;oBACb,OAAO;oBACP,SAAS;gBACX;YACF;YACA;gBACE,KAAK;gBACL,MAAM;gBACN,UAAU;gBACV,MAAM;gBACN,cAAc;gBACd,OAAO;oBACL,aAAa;oBACb,OAAO;oBACP,SAAS;gBACX;YACF;SACD;IACH;IAEA,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAChC,QAAQ;YACN;gBACE,KAAK;gBACL,MAAM;gBACN,MAAM;gBACN,KAAK;YACP;YACA;gBACE,KAAK;gBACL,MAAM;gBACN,MAAM;gBACN,KAAK;YACP;YACA;gBACE,KAAK;gBACL,MAAM;gBACN,MAAM;gBACN,KAAK;YACP;SACD;IACH;IAEA,qBACE;;0BACE,8OAAC,qHAAA,CAAA,UAAM;;;;;0BACP,8OAAC;;kCAEC,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAAoB;;;;;;;;;;;;;;;;;kCAOrC,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAuC;;;;;;gCAGpD,CAAC;oCACA,MAAM,UAAU,QAAQ,IAAI,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;oCACvD,IAAI,SAAS;wCACX,qBACE,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAG,WAAU;8DAAuD;;;;;;8DACrE,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;oEACZ,QAAQ,KAAK,iBACZ,8OAAC,6HAAA,CAAA,UAAK;wEACJ,KAAK,CAAA,GAAA,6GAAA,CAAA,cAAW,AAAD,EAAE,QAAQ,KAAK,EAAE,GAAG;wEACnC,KAAK,QAAQ,IAAI;wEACjB,IAAI;wEACJ,WAAU;;;;;6FAGZ,8OAAC,6HAAA,CAAA,UAAK;wEACJ,KAAI;wEACJ,KAAK,QAAQ,IAAI;wEACjB,IAAI;wEACJ,WAAU;;;;;;kFAGd,8OAAC;wEAAI,WAAU;;4EAAwE;4EACnF,QAAQ,YAAY;;;;;;;kFAExB,8OAAC;wEAAI,WAAU;kFAAkF;;;;;;;;;;;;0EAKnG,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAG,WAAU;kFAAuC,QAAQ,IAAI;;;;;;kFACjE,8OAAC;wEAAE,WAAU;kFAAyD,QAAQ,QAAQ;;;;;;kFAEtF,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;;kGACC,8OAAC;wFAAE,WAAU;kGAAuC;;;;;;kGACpD,8OAAC;wFAAE,WAAU;kGAAsC,QAAQ,KAAK,EAAE,eAAe;;;;;;;;;;;;0FAEnF,8OAAC;;kGACC,8OAAC;wFAAE,WAAU;kGAAuC;;;;;;kGACpD,8OAAC;wFAAE,WAAU;kGACV,QAAQ,QAAQ,KAAK,eAAe,MAAM,QAAQ,KAAK,EAAE,SAAS;;;;;;;;;;;;0FAGvE,8OAAC;;kGACC,8OAAC;wFAAE,WAAU;kGACV,QAAQ,QAAQ,KAAK,eAAe,iBAAiB;;;;;;kGAExD,8OAAC;wFAAE,WAAU;kGACV,QAAQ,QAAQ,KAAK,eAClB,QAAQ,KAAK,EAAE,eAAe,IAC9B,QAAQ,KAAK,EAAE,WAAW;;;;;;;;;;;;;;;;;;oEAMnC,QAAQ,GAAG,kBACV,8OAAC;wEAAI,WAAU;kFACb,cAAA,8OAAC;4EAAE,WAAU;sFAAwC,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oCAQhF;oCACA,OAAO;gCACT,CAAC;gCAGA,CAAC;oCACA,MAAM,eAAe,QAAQ,MAAM,CAAC,CAAA,SAAU,OAAO,IAAI,KAAK;oCAC9D,IAAI,aAAa,MAAM,GAAG,GAAG;wCAC3B,oDAAoD;wCACpD,MAAM,oBAAoB,aAAa,MAAM,CAAC,CAAC,KAAK;4CAClD,MAAM,WAAW,OAAO,QAAQ;4CAChC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE;gDAClB,GAAG,CAAC,SAAS,GAAG,EAAE;4CACpB;4CACA,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;4CACnB,OAAO;wCACT,GAAG,CAAC;wCAEJ,MAAM,gBAAgB;4CAAC;4CAAc;4CAAY;4CAAc;yCAAU;wCACzE,MAAM,mBAAmB,CAAC;4CACxB,OAAQ;gDACN,KAAK;oDAAc,OAAO;gDAC1B,KAAK;oDAAY,OAAO;gDACxB,KAAK;oDAAc,OAAO;gDAC1B,KAAK;oDAAW,OAAO;gDACvB;oDAAS,OAAO;4CAClB;wCACF;wCAEA,qBACE,8OAAC;;8DACC,8OAAC;oDAAG,WAAU;8DAAsC;;;;;;gDAEnD,cAAc,GAAG,CAAC,CAAC;oDAClB,MAAM,kBAAkB,iBAAiB,CAAC,SAAS;oDACnD,IAAI,CAAC,mBAAmB,gBAAgB,MAAM,KAAK,GAAG,OAAO;oDAE7D,qBACE,8OAAC;wDAAmB,WAAU;;0EAC5B,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;wEAAI,WAAW,GAAG,iBAAiB,UAAU,0BAA0B,CAAC;;;;;;kFACzE,8OAAC;wEAAG,WAAU;;4EAAoC;4EAAS;;;;;;;kFAC3D,8OAAC;wEAAI,WAAW,GAAG,iBAAiB,UAAU,0BAA0B,CAAC;;;;;;;;;;;;0EAG3E,8OAAC;gEAAI,WAAU;0EACZ,gBAAgB,GAAG,CAAC,CAAC,uBACpB,8OAAC;wEAAqB,WAAU;;0FAE9B,8OAAC;gFAAI,WAAU;;oFACZ,OAAO,KAAK,iBACX,8OAAC,6HAAA,CAAA,UAAK;wFACJ,KAAK,CAAA,GAAA,6GAAA,CAAA,cAAW,AAAD,EAAE,OAAO,KAAK,EAAE,GAAG;wFAClC,KAAK,OAAO,IAAI;wFAChB,IAAI;wFACJ,WAAU;;;;;6GAGZ,8OAAC;wFAAI,WAAU;kGACb,cAAA,8OAAC;4FAAI,WAAU;4FAA6B,MAAK;4FAAO,QAAO;4FAAe,SAAQ;sGACpF,cAAA,8OAAC;gGAAK,eAAc;gGAAQ,gBAAe;gGAAQ,aAAY;gGAAI,GAAE;;;;;;;;;;;;;;;;kGAM3E,8OAAC;wFAAI,WAAW,CAAC,sBAAsB,EAAE,iBAAiB,OAAO,QAAQ,EAAE,8DAA8D,CAAC;;4FAAE;4FACxI,OAAO,YAAY;;;;;;;kGAIvB,8OAAC;wFAAI,WAAU;kGACZ,OAAO,QAAQ;;;;;;oFAIjB,OAAO,IAAI,KAAK,gCACf,8OAAC;wFAAI,WAAU;kGAAiG;;;;;;;;;;;;0FAOpH,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAG,WAAU;kGACX,OAAO,IAAI;;;;;;kGAId,8OAAC;wFAAI,WAAU;;0GACb,8OAAC;gGAAI,WAAU;;kHACb,8OAAC;wGAAE,WAAU;kHAAyC;;;;;;kHACtD,8OAAC;wGAAE,WAAU;kHAAmC,OAAO,KAAK,EAAE,eAAe;;;;;;;;;;;;0GAE/E,8OAAC;gGAAI,WAAU;;kHACb,8OAAC;wGAAE,WAAU;kHACV,OAAO,QAAQ,KAAK,eAAe,UAAU;;;;;;kHAEhD,8OAAC;wGAAE,WAAU;kHACV,OAAO,QAAQ,KAAK,eAAe,MAAM,OAAO,KAAK,EAAE,SAAS;;;;;;;;;;;;0GAGrE,8OAAC;gGAAI,WAAU;;kHACb,8OAAC;wGAAE,WAAU;kHACV,OAAO,QAAQ,KAAK,eAAe,UAAU;;;;;;kHAEhD,8OAAC;wGAAE,WAAU;kHACV,OAAO,QAAQ,KAAK,eACjB,OAAO,KAAK,EAAE,eAAe,IAC7B,OAAO,KAAK,EAAE,WAAW;;;;;;;;;;;;;;;;;;oFAOlC,OAAO,GAAG,kBACT,8OAAC;wFAAE,WAAU;kGACV,OAAO,GAAG;;;;;;;;;;;;;uEAxET,OAAO,GAAG;;;;;;;;;;;uDAThB;;;;;gDA0Fd;;;;;;;oCAGN;oCACA,OAAO;gCACT,CAAC;;;;;;;;;;;;kCAKL,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAwC;;;;;;sDACtD,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC;4CAAE,WAAU;sDAAkC;;;;;;;;;;;;8CAKjD,8OAAC;oCAAI,WAAU;8CACZ,MAAM,GAAG,CAAC,CAAC,uBACV,8OAAC;4CAAqB,WAAU;;8DAE9B,8OAAC;oDAAI,WAAU;;wDACZ,OAAO,KAAK,iBACX,8OAAC,6HAAA,CAAA,UAAK;4DACJ,KAAK,CAAA,GAAA,6GAAA,CAAA,cAAW,AAAD,EAAE,OAAO,KAAK,EAAE,GAAG;4DAClC,KAAK,OAAO,IAAI;4DAChB,IAAI;4DACJ,WAAU;;;;;iFAGZ,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAI,WAAU;gEAA6B,MAAK;gEAAO,QAAO;gEAAe,SAAQ;0EACpF,cAAA,8OAAC;oEAAK,eAAc;oEAAQ,gBAAe;oEAAQ,aAAY;oEAAI,GAAE;;;;;;;;;;;;;;;;sEAM3E,8OAAC;4DAAI,WAAU;sEACZ,OAAO,IAAI;;;;;;;;;;;;8DAKhB,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;4DAAG,WAAU;sEACX,OAAO,IAAI;;;;;;wDAGb,OAAO,GAAG,kBACT,8OAAC;4DAAE,WAAU;sEACV,OAAO,GAAG;;;;;;wDAKd,OAAO,WAAW,IAAI,CAAC,OAAO,WAAW,CAAC,KAAK,IAAI,OAAO,WAAW,CAAC,KAAK,mBAC1E,8OAAC;4DAAI,WAAU;;8EACb,8OAAC;oEAAG,WAAU;8EAA2C;;;;;;gEACxD,OAAO,WAAW,CAAC,KAAK,kBACvB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;4EAAgC,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFACpF,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAa;gFAAG,GAAE;;;;;;;;;;;sFAEvE,8OAAC;4EAAK,WAAU;sFAAyB,OAAO,WAAW,CAAC,KAAK;;;;;;;;;;;;gEAGpE,OAAO,WAAW,CAAC,KAAK,kBACvB,8OAAC;oEAAI,WAAU;;sFACb,8OAAC;4EAAI,WAAU;4EAAgC,MAAK;4EAAO,SAAQ;4EAAY,QAAO;sFACpF,cAAA,8OAAC;gFAAK,eAAc;gFAAQ,gBAAe;gFAAQ,aAAa;gFAAG,GAAE;;;;;;;;;;;sFAEvE,8OAAC;4EAAK,WAAU;sFAAyB,OAAO,WAAW,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;2CArDnE,OAAO,GAAG;;;;;;;;;;;;;;;;;;;;;kCAkE5B,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAG,WAAU;kDAA0B;;;;;;kDACxC,8OAAC;wCAAE,WAAU;kDAAe;;;;;;kDAG5B,8OAAC;wCACC,MAAK;wCACL,WAAU;;4CACX;0DAEC,8OAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAAe,SAAQ;gDAAY,MAAK;0DACxF,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAA0I,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1L,8OAAC,qHAAA,CAAA,UAAM;;;;;;;AAGb"}}, {"offset": {"line": 1724, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1735, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 1735, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}