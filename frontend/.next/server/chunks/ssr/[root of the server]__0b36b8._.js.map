{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/Header.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/Header.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAyR,GACtT,uDACA"}}, {"offset": {"line": 15, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 21, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Header.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/components/Header.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/Header.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": ";;;AAAA;;uCACe,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EACjC;IAAa,MAAM,IAAI,MAAM;AAAqQ,GAClS,mCACA"}}, {"offset": {"line": 29, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 35, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 39, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/components/Footer.tsx"], "sourcesContent": ["import Link from 'next/link'\n\nexport default function Footer() {\n  const currentYear = new Date().getFullYear()\n\n  return (\n    <footer className=\"bg-gradient-to-b from-neutral-800 to-neutral-900 text-white\">\n      <div className=\"container py-12\">\n        <div className=\"grid grid-cols-1 md:grid-cols-4 gap-8\">\n          <div className=\"md:col-span-2\">\n            <h3 className=\"text-xl font-bold mb-4 text-white\">Northern Nepalese United FC</h3>\n            <p className=\"mb-4 text-neutral-300\">\n              Brisbane-based Nepalese football club est. 2023, bringing together the community through the beautiful game.\n            </p>\n            <div className=\"flex space-x-4 mt-6\">\n              <a\n                href=\"https://facebook.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                aria-label=\"Facebook\"\n                className=\"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"currentColor\"\n                >\n                  <path d=\"M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z\" />\n                </svg>\n              </a>\n              <a\n                href=\"https://twitter.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                aria-label=\"Twitter\"\n                className=\"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"currentColor\"\n                >\n                  <path d=\"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z\" />\n                </svg>\n              </a>\n              <a\n                href=\"https://instagram.com\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                aria-label=\"Instagram\"\n                className=\"bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-full transition-colors\"\n              >\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  width=\"24\"\n                  height=\"24\"\n                  viewBox=\"0 0 24 24\"\n                  fill=\"currentColor\"\n                >\n                  <path d=\"M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z\" />\n                </svg>\n              </a>\n            </div>\n          </div>\n\n          <div>\n            <h3 className=\"text-xl font-bold mb-4 text-white\">Club Links</h3>\n            <ul className=\"space-y-2 text-neutral-300\">\n              <li>\n                <Link href=\"/about\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  About Us\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/team\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  Our Team\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/news\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  News\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/events\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  Events & Fixtures\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/gallery\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  Gallery\n                </Link>\n              </li>\n            </ul>\n          </div>\n\n          <div>\n            <h3 className=\"text-xl font-bold mb-4 text-white\">Contact</h3>\n            <address className=\"not-italic text-neutral-300\">\n              <p className=\"mb-2 flex items-start\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 text-neutral-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z\" />\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 11a3 3 0 11-6 0 3 3 0 016 0z\" />\n                </svg>\n                John Oxley Reserve<br />Murrumba Downs<br />Brisbane, QLD\n              </p>\n              <p className=\"mb-2 flex items-start\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 text-neutral-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\" />\n                </svg>\n                <a href=\"tel:+61424770570\" className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\">\n                  0424 770 570\n                </a>\n              </p>\n              <p className=\"flex items-start\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 mr-2 mt-0.5 text-neutral-400\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\" />\n                </svg>\n                <a\n                  href=\"mailto:<EMAIL>\"\n                  className=\"text-neutral-300 hover:text-white hover:underline transition-colors no-underline\"\n                >\n                  <EMAIL>\n                </a>\n              </p>\n            </address>\n          </div>\n        </div>\n\n        <div className=\"border-t border-neutral-700 mt-10 pt-8 text-center text-neutral-400\">\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p>&copy; {currentYear} Northern Nepalese United - NNUFC. All rights reserved.</p>\n            <ul className=\"flex space-x-4 mt-4 md:mt-0\">\n              <li>\n                <Link href=\"/sponsors\" className=\"text-neutral-400 hover:text-white hover:underline transition-colors no-underline\">\n                  Sponsors\n                </Link>\n              </li>\n              <li>\n                <Link href=\"/contact\" className=\"text-neutral-400 hover:text-white hover:underline transition-colors no-underline\">\n                  Contact\n                </Link>\n              </li>\n            </ul>\n          </div>\n        </div>\n      </div>\n    </footer>\n  )\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AAEe,SAAS;IACtB,MAAM,cAAc,IAAI,OAAO,WAAW;IAE1C,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAE,WAAU;8CAAwB;;;;;;8CAGrC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,cAAW;4CACX,WAAU;sDAEV,cAAA,8OAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;0DAEL,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,cAAW;4CACX,WAAU;sDAEV,cAAA,8OAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;0DAEL,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;sDAGZ,8OAAC;4CACC,MAAK;4CACL,QAAO;4CACP,KAAI;4CACJ,cAAW;4CACX,WAAU;sDAEV,cAAA,8OAAC;gDACC,OAAM;gDACN,OAAM;gDACN,QAAO;gDACP,SAAQ;gDACR,MAAK;0DAEL,cAAA,8OAAC;oDAAK,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAMhB,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAG,WAAU;;sDACZ,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAS,WAAU;0DAAmF;;;;;;;;;;;sDAInH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmF;;;;;;;;;;;sDAIlH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAQ,WAAU;0DAAmF;;;;;;;;;;;sDAIlH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAU,WAAU;0DAAmF;;;;;;;;;;;sDAIpH,8OAAC;sDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;gDAAC,MAAK;gDAAW,WAAU;0DAAmF;;;;;;;;;;;;;;;;;;;;;;;sCAOzH,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,8OAAC;oCAAQ,WAAU;;sDACjB,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAuC,MAAK;oDAAO,SAAQ;oDAAY,QAAO;;sEAC9H,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;sEACrE,8OAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;gDACjE;8DACY,8OAAC;;;;;gDAAK;8DAAc,8OAAC;;;;;gDAAK;;;;;;;sDAE9C,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAuC,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAC9H,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,8OAAC;oDAAE,MAAK;oDAAmB,WAAU;8DAAmF;;;;;;;;;;;;sDAI1H,8OAAC;4CAAE,WAAU;;8DACX,8OAAC;oDAAI,OAAM;oDAA6B,WAAU;oDAAuC,MAAK;oDAAO,SAAQ;oDAAY,QAAO;8DAC9H,cAAA,8OAAC;wDAAK,eAAc;wDAAQ,gBAAe;wDAAQ,aAAa;wDAAG,GAAE;;;;;;;;;;;8DAEvE,8OAAC;oDACC,MAAK;oDACL,WAAU;8DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAQT,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;oCAAE;oCAAQ;oCAAY;;;;;;;0CACvB,8OAAC;gCAAG,WAAU;;kDACZ,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAY,WAAU;sDAAmF;;;;;;;;;;;kDAItH,8OAAC;kDACC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAW,WAAU;sDAAmF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnI"}}, {"offset": {"line": 525, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/lib/sanity.ts"], "sourcesContent": ["import { createClient } from 'next-sanity'\nimport imageUrlBuilder from '@sanity/image-url'\n\nconst projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID!\nconst dataset = process.env.NEXT_PUBLIC_SANITY_DATASET!\nconst apiVersion = process.env.NEXT_PUBLIC_SANITY_API_VERSION || '2024-05-23'\n\nexport const client = createClient({\n  projectId,\n  dataset,\n  apiVersion,\n  useCdn: typeof document !== 'undefined',\n})\n\n// Helper function for generating image URLs with the Sanity Image Pipeline\nconst builder = imageUrlBuilder({\n  projectId,\n  dataset,\n})\n\nexport function urlForImage(source: any) {\n  return builder.image(source)\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEA,MAAM;AACN,MAAM;AACN,MAAM,aAAa,kDAA8C;AAE1D,MAAM,SAAS,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE;IACjC;IACA;IACA;IACA,QAAQ,OAAO,aAAa;AAC9B;AAEA,2EAA2E;AAC3E,MAAM,UAAU,CAAA,GAAA,gKAAA,CAAA,UAAe,AAAD,EAAE;IAC9B;IACA;AACF;AAEO,SAAS,YAAY,MAAW;IACrC,OAAO,QAAQ,KAAK,CAAC;AACvB"}}, {"offset": {"line": 564, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 570, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/Developer/projects/processing/brisbane-fc-website/frontend/src/app/events/page.tsx"], "sourcesContent": ["import Header from \"@/components/Header\";\nimport Footer from \"@/components/Footer\";\nimport Link from \"next/link\";\nimport { client } from \"@/lib/sanity\";\nimport { Event } from \"@/types\";\n\nexport const metadata = {\n  title: \"Events & Fixtures | Northern Nepalese United FC\",\n  description: \"View upcoming matches, training sessions, and social events for Northern Nepalese United Football Club.\",\n};\n\n// This makes the page dynamic and will revalidate every 60 seconds\nexport const revalidate = 60;\n\nasync function getEvents() {\n  return await client.fetch(`\n    *[_type == \"event\"] | order(date desc) {\n      _id,\n      title,\n      slug,\n      date,\n      location,\n      eventType,\n      opponent,\n      homeOrAway,\n      result\n    }\n  `);\n}\n\nexport default async function EventsPage() {\n  const allEvents = await getEvents();\n  \n  // Split events into upcoming and past\n  const now = new Date().toISOString();\n  const upcomingEvents = allEvents.filter((event: Event) => event.date > now);\n  const pastEvents = allEvents.filter((event: Event) => event.date <= now);\n  \n  // Sort upcoming events by date (ascending - nearest first)\n  upcomingEvents.sort((a: Event, b: Event) => new Date(a.date).getTime() - new Date(b.date).getTime());\n  \n  // Sort past events by date (descending - most recent first)\n  pastEvents.sort((a: Event, b: Event) => new Date(b.date).getTime() - new Date(a.date).getTime());\n  \n  return (\n    <>\n      <Header />\n      <main>\n        {/* Hero Section */}\n        <section className=\"bg-primary-800 text-white py-16\">\n          <div className=\"container\">\n            <h1 className=\"text-4xl md:text-5xl font-bold mb-4\">Events & Fixtures</h1>\n            <p className=\"text-xl max-w-3xl\">\n              Stay updated with all upcoming matches, training sessions, and social events for Northern Nepalese United FC.\n            </p>\n          </div>\n        </section>\n\n        {/* Upcoming Events Section */}\n        <section className=\"py-16\">\n          <div className=\"container\">\n            <h2 className=\"text-3xl font-bold mb-8\">Upcoming Events</h2>\n            \n            {upcomingEvents.length > 0 ? (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {upcomingEvents.map((event: Event) => (\n                  <div key={event._id} className={`\n                    bg-white border border-neutral-200 rounded-lg p-8 shadow-lg\n                    hover:scale-105 transform transition-all duration-300 ease-out\n                    border-t-4 ${\n                      event.eventType === \"match\" ? \"border-t-primary-500\" :\n                      event.eventType === \"training\" ? \"border-t-secondary-500\" :\n                      \"border-t-neutral-500\"\n                    }\n                  `}>\n                    <div className=\"flex justify-between items-start\">\n                      <div>\n                        <h3 className=\"text-xl font-bold mb-2\">{event.title}</h3>\n                        <p className=\"text-neutral-600 mb-1\">\n                          {new Date(event.date).toLocaleDateString(\"en-US\", {\n                            weekday: \"long\",\n                            year: \"numeric\",\n                            month: \"long\",\n                            day: \"numeric\",\n                          })}\n                          {\" at \"}\n                          {new Date(event.date).toLocaleTimeString(\"en-US\", {\n                            hour: \"2-digit\",\n                            minute: \"2-digit\",\n                          })}\n                        </p>\n                        {event.location && <p className=\"text-neutral-600 mb-2\">{event.location}</p>}\n                      </div>\n                      {event.eventType && (\n                        <span className={`text-sm px-3 py-1.5 rounded uppercase font-bold ${\n                          event.eventType === \"match\" \n                            ? \"bg-primary-100 text-primary-800\" \n                            : event.eventType === \"training\" \n                            ? \"bg-secondary-100 text-secondary-800\"\n                            : \"bg-neutral-100 text-neutral-800\"\n                        }`}>\n                          {event.eventType}\n                        </span>\n                      )}\n                    </div>\n\n                    {event.eventType === \"match\" && event.opponent && (\n                      <div className=\"mt-3\">\n                        <p className=\"text-neutral-800\">\n                          <span className=\"font-medium\">\n                            {event.homeOrAway === \"Home\" ? \"Home game vs \" : \"Away game @ \"}\n                            {event.opponent}\n                          </span>\n                        </p>\n                      </div>\n                    )}\n\n                    {event.slug && (\n                      <Link\n                        href={`/events/${event.slug.current}`}\n                        className=\"mt-4 inline-block bg-primary-600 text-white px-4 py-2 rounded-md font-semibold hover:bg-primary-700 transition-colors duration-300\"\n                      >\n                        View Details →\n                      </Link>\n                    )}\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"bg-neutral-50 rounded-lg p-8 text-center\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  className=\"h-12 w-12 text-neutral-400 mx-auto mb-4\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={1}\n                    d=\"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z\"\n                  />\n                </svg>\n                <h3 className=\"text-xl font-bold mb-2\">No Upcoming Events</h3>\n                <p className=\"text-neutral-600\">\n                  Check back soon for our upcoming fixtures and events.\n                </p>\n              </div>\n            )}\n          </div>\n        </section>\n\n        {/* Past Events Section */}\n        <section className=\"py-16 bg-neutral-50\">\n          <div className=\"container\">\n            <h2 className=\"text-3xl font-bold mb-8\">Past Events</h2>\n            \n            {pastEvents.length > 0 ? (\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                {pastEvents.map((event: Event) => (\n                  <div key={event._id} className={`\n                    bg-neutral-100 border border-neutral-300 rounded-lg p-8 shadow-md\n                    hover:scale-105 transform transition-all duration-300 ease-out\n                    border-t-4 ${\n                      event.eventType === \"match\" ? \"border-t-primary-400\" :\n                      event.eventType === \"training\" ? \"border-t-secondary-400\" :\n                      \"border-t-neutral-400\"\n                    }\n                  `}>\n                    <div className=\"flex justify-between items-start\">\n                      <div>\n                        <h3 className=\"text-xl font-bold mb-2\">{event.title}</h3>\n                        <p className=\"text-neutral-600 mb-1\">\n                          {new Date(event.date).toLocaleDateString(\"en-US\", {\n                            weekday: \"long\",\n                            year: \"numeric\",\n                            month: \"long\",\n                            day: \"numeric\",\n                          })}\n                        </p>\n                        {event.location && <p className=\"text-neutral-600 mb-2\">{event.location}</p>}\n                      </div>\n                      {event.eventType && (\n                        <span className={`text-sm px-3 py-1.5 rounded uppercase font-bold ${\n                          event.eventType === \"match\" \n                            ? \"bg-primary-100 text-primary-800\" \n                            : event.eventType === \"training\" \n                            ? \"bg-secondary-100 text-secondary-800\"\n                            : \"bg-neutral-100 text-neutral-800\"\n                        }`}>\n                          {event.eventType}\n                        </span>\n                      )}\n                    </div>\n\n                    {event.eventType === \"match\" && (\n                      <div className=\"mt-3\">\n                        <p className=\"text-neutral-800\">\n                          <span className=\"font-medium\">\n                            {event.homeOrAway === \"Home\" ? \"Home game vs \" : \"Away game @ \"}\n                            {event.opponent}\n                          </span>\n                          {event.result && (\n                            <span className=\"ml-2 text-neutral-600\">\n                              ({event.result})\n                            </span>\n                          )}\n                        </p>\n                      </div>\n                    )}\n\n                    {event.slug && (\n                      <Link\n                        href={`/events/${event.slug.current}`}\n                        className=\"mt-4 inline-block bg-primary-600 text-white px-4 py-2 rounded-md font-semibold hover:bg-primary-700 transition-colors duration-300\"\n                      >\n                        View Details →\n                      </Link>\n                    )}\n                  </div>\n                ))}\n              </div>\n            ) : (\n              <div className=\"bg-white rounded-lg p-8 text-center\">\n                <svg\n                  xmlns=\"http://www.w3.org/2000/svg\"\n                  className=\"h-12 w-12 text-neutral-400 mx-auto mb-4\"\n                  fill=\"none\"\n                  viewBox=\"0 0 24 24\"\n                  stroke=\"currentColor\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={1}\n                    d=\"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z\"\n                  />\n                </svg>\n                <h3 className=\"text-xl font-bold mb-2\">No Past Events</h3>\n                <p className=\"text-neutral-600\">\n                  Our event history will appear here after events have taken place.\n                </p>\n              </div>\n            )}\n          </div>\n        </section>\n      </main>\n      <Footer />\n    </>\n  );\n} "], "names": [], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;;;;;;AAGO,MAAM,WAAW;IACtB,OAAO;IACP,aAAa;AACf;AAGO,MAAM,aAAa;AAE1B,eAAe;IACb,OAAO,MAAM,6GAAA,CAAA,SAAM,CAAC,KAAK,CAAC,CAAC;;;;;;;;;;;;EAY3B,CAAC;AACH;AAEe,eAAe;IAC5B,MAAM,YAAY,MAAM;IAExB,sCAAsC;IACtC,MAAM,MAAM,IAAI,OAAO,WAAW;IAClC,MAAM,iBAAiB,UAAU,MAAM,CAAC,CAAC,QAAiB,MAAM,IAAI,GAAG;IACvE,MAAM,aAAa,UAAU,MAAM,CAAC,CAAC,QAAiB,MAAM,IAAI,IAAI;IAEpE,2DAA2D;IAC3D,eAAe,IAAI,CAAC,CAAC,GAAU,IAAa,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;IAEjG,4DAA4D;IAC5D,WAAW,IAAI,CAAC,CAAC,GAAU,IAAa,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,IAAI,EAAE,OAAO;IAE7F,qBACE;;0BACE,8OAAC,qHAAA,CAAA,UAAM;;;;;0BACP,8OAAC;;kCAEC,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsC;;;;;;8CACpD,8OAAC;oCAAE,WAAU;8CAAoB;;;;;;;;;;;;;;;;;kCAOrC,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;gCAEvC,eAAe,MAAM,GAAG,kBACvB,8OAAC;oCAAI,WAAU;8CACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC;4CAAoB,WAAW,CAAC;;;+BAGpB,EACT,MAAM,SAAS,KAAK,UAAU,yBAC9B,MAAM,SAAS,KAAK,aAAa,2BACjC,uBACD;kBACH,CAAC;;8DACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA0B,MAAM,KAAK;;;;;;8EACnD,8OAAC;oEAAE,WAAU;;wEACV,IAAI,KAAK,MAAM,IAAI,EAAE,kBAAkB,CAAC,SAAS;4EAChD,SAAS;4EACT,MAAM;4EACN,OAAO;4EACP,KAAK;wEACP;wEACC;wEACA,IAAI,KAAK,MAAM,IAAI,EAAE,kBAAkB,CAAC,SAAS;4EAChD,MAAM;4EACN,QAAQ;wEACV;;;;;;;gEAED,MAAM,QAAQ,kBAAI,8OAAC;oEAAE,WAAU;8EAAyB,MAAM,QAAQ;;;;;;;;;;;;wDAExE,MAAM,SAAS,kBACd,8OAAC;4DAAK,WAAW,CAAC,gDAAgD,EAChE,MAAM,SAAS,KAAK,UAChB,oCACA,MAAM,SAAS,KAAK,aACpB,wCACA,mCACJ;sEACC,MAAM,SAAS;;;;;;;;;;;;gDAKrB,MAAM,SAAS,KAAK,WAAW,MAAM,QAAQ,kBAC5C,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;kEACX,cAAA,8OAAC;4DAAK,WAAU;;gEACb,MAAM,UAAU,KAAK,SAAS,kBAAkB;gEAChD,MAAM,QAAQ;;;;;;;;;;;;;;;;;gDAMtB,MAAM,IAAI,kBACT,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE;oDACrC,WAAU;8DACX;;;;;;;2CAvDK,MAAM,GAAG;;;;;;;;;yDA+DvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,OAAM;4CACN,WAAU;4CACV,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;sDAGN,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAmB;;;;;;;;;;;;;;;;;;;;;;;kCASxC,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA0B;;;;;;gCAEvC,WAAW,MAAM,GAAG,kBACnB,8OAAC;oCAAI,WAAU;8CACZ,WAAW,GAAG,CAAC,CAAC,sBACf,8OAAC;4CAAoB,WAAW,CAAC;;;+BAGpB,EACT,MAAM,SAAS,KAAK,UAAU,yBAC9B,MAAM,SAAS,KAAK,aAAa,2BACjC,uBACD;kBACH,CAAC;;8DACC,8OAAC;oDAAI,WAAU;;sEACb,8OAAC;;8EACC,8OAAC;oEAAG,WAAU;8EAA0B,MAAM,KAAK;;;;;;8EACnD,8OAAC;oEAAE,WAAU;8EACV,IAAI,KAAK,MAAM,IAAI,EAAE,kBAAkB,CAAC,SAAS;wEAChD,SAAS;wEACT,MAAM;wEACN,OAAO;wEACP,KAAK;oEACP;;;;;;gEAED,MAAM,QAAQ,kBAAI,8OAAC;oEAAE,WAAU;8EAAyB,MAAM,QAAQ;;;;;;;;;;;;wDAExE,MAAM,SAAS,kBACd,8OAAC;4DAAK,WAAW,CAAC,gDAAgD,EAChE,MAAM,SAAS,KAAK,UAChB,oCACA,MAAM,SAAS,KAAK,aACpB,wCACA,mCACJ;sEACC,MAAM,SAAS;;;;;;;;;;;;gDAKrB,MAAM,SAAS,KAAK,yBACnB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAE,WAAU;;0EACX,8OAAC;gEAAK,WAAU;;oEACb,MAAM,UAAU,KAAK,SAAS,kBAAkB;oEAChD,MAAM,QAAQ;;;;;;;4DAEhB,MAAM,MAAM,kBACX,8OAAC;gEAAK,WAAU;;oEAAwB;oEACpC,MAAM,MAAM;oEAAC;;;;;;;;;;;;;;;;;;gDAOxB,MAAM,IAAI,kBACT,8OAAC,4JAAA,CAAA,UAAI;oDACH,MAAM,CAAC,QAAQ,EAAE,MAAM,IAAI,CAAC,OAAO,EAAE;oDACrC,WAAU;8DACX;;;;;;;2CAvDK,MAAM,GAAG;;;;;;;;;yDA+DvB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,OAAM;4CACN,WAAU;4CACV,MAAK;4CACL,SAAQ;4CACR,QAAO;sDAEP,cAAA,8OAAC;gDACC,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;sDAGN,8OAAC;4CAAG,WAAU;sDAAyB;;;;;;sDACvC,8OAAC;4CAAE,WAAU;sDAAmB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ1C,8OAAC,qHAAA,CAAA,UAAM;;;;;;;AAGb"}}, {"offset": {"line": 1045, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}, {"offset": {"line": 1056, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 1056, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "A"}}]}